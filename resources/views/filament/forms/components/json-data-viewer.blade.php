<div class="p-2 bg-white dark:bg-gray-800 rounded-lg shadow">
    @php
        $data = $getState();

        // Helper function to resolve IDs to names
        function resolveIdToName($key, $value) {
            // Handle user_id, created_by, updated_by, deleted_by
            if ($key === 'user_id' || $key === 'created_by' || $key === 'updated_by' || $key === 'deleted_by') {
                if (!$value) return '-';
                $user = \App\Models\User::find($value);
                return $user ? $user->name : $value;
            }

            if ($key === 'subscription_id') {
                $subscription = \App\Models\Subscription::find($value);
                return $subscription ? $subscription->name : $value;
            }

            if ($key === 'plan_id') {
                $plan = \App\Models\Plan::find($value);
                return $plan ? $plan->name : $value;
            }

            if ($key === 'company_id') {
                $company = \App\Models\Company::find($value);
                return $company ? $company->name : $value;
            }

            if ($key === 'country_id' || $key === 'billing_country_id') {
                $country = \App\Models\Country::find($value);
                return $country ? $country->name : $value;
            }

            if ($key === 'currency_id') {
                $currency = \App\Models\Currency::find($value);
                return $currency ? $currency->name : $value;
            }
            
            if ($key === 'status_id') {
                $status = \App\Models\Status::find($value);
                return $status ? $status->name : $value;
            }
            
            if ($key === 'suspension_reason_id') {
                $reason = \App\Models\SuspensionReason::find($value);
                return $reason ? $reason->name : $value;
            }
            
            // Return original value if no special handling
            return $value;
        }

        // Updated categories with new order
        $categories = [
            'Subscription Details' => [
                'plan_id', 'subscription_type', 'plan_features', 'addon_features',
                'assigned_quota', 'used_quota', 'remaining_quota', 'subscription_status',
                'auto_renew'
            ],
            'Subscriber Details' => [
                'subscriber_type', 'subscriber_id', 'country_id', 'public_subscription_id'
            ],
            'Payment Details' => [
                'currency_id', 'payment_status', 'billing_country_id', 'last_payment_date'
            ],
            'Subscription License Details' => [
                'subscription_licenses'
            ],
            'Payment Rejection Details' => [
                'rejection_reason', 'rejection_reason_description'
            ],
            'Dates' => [
                'trial_ends_at', 'starts_at', 'ends_at', 'canceled_at', 'cancels_at'
            ],
            'Audit' => [
                'created_at', 'created_by', 'updated_at', 'updated_by', 'deleted_at', 'deleted_by'
            ]
        ];

        // Create reverse mapping
        $keyToCategory = [];
        foreach ($categories as $category => $keys) {
            foreach ($keys as $key) {
                $keyToCategory[$key] = $category;
            }
        }

        $grouped = [];
        foreach ((array)$data as $key => $value) {
            if (isset($keyToCategory[$key])) {
                $grouped[$keyToCategory[$key]][$key] = $value;
            } else {
                $grouped['Other'][$key] = $value;
            }
        }

        // Define section display order
        $sectionOrder = [
            'Subscription Details',
            'Subscriber Details',
            'Payment Details',
            'Subscription License Details',
            'Payment Rejection Details',
            'Dates',
            'Audit',
            'Other'
        ];
    @endphp

    @if(is_array($data) || is_object($data))
<<<<<<< Updated upstream
        <div class="overflow-x-auto">
            <table class="w-full text-sm">
                <thead>
                    <tr>
                        <th class="py-2 px-4 text-left font-medium text-gray-700 dark:text-gray-200">Property</th>
                        <th class="py-2 px-4 text-left font-medium text-gray-700 dark:text-gray-200">Value</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach((array)$data as $key => $value)
                        <tr class="border-t dark:border-gray-700">
                            <td class="py-2 px-4 font-medium text-gray-700 dark:text-gray-200">
                                {{ str_ends_with($key, '_id') ? str_replace('_id', '', ucfirst(str_replace('_', ' ', $key))) : ucfirst(str_replace('_', ' ', $key)) }}
                            </td>
                            <td class="py-2 px-4 text-gray-700 dark:text-gray-200">
                                @if(is_array($value) || is_object($value))
                                    <details>
                                        <summary class="cursor-pointer text-primary-600 hover:text-primary-500">
                                            {{ is_array($value) ? 'Array' : 'Object' }} ({{ count((array)$value) }} items)
                                        </summary>
                                        <div class="mt-2 pl-4 border-l-2 border-gray-200 dark:border-gray-700">
                                            <table class="w-full text-sm">
                                                <tbody>
                                                    @foreach((array)$value as $nestedKey => $nestedValue)
                                                        <tr class="border-t dark:border-gray-700">
                                                            <td class="py-2 px-4 font-medium text-gray-700 dark:text-gray-200 bg-gray-50 dark:bg-gray-700">{{ $nestedKey }}</td>
                                                            <td class="py-2 px-4 dark:text-gray-200">
                                                                @if(is_array($nestedValue) || is_object($nestedValue))
                                                                    <code class="text-xs bg-gray-100 dark:bg-gray-700 p-1 rounded">{{ json_encode($nestedValue, JSON_PRETTY_PRINT) }}</code>
                                                                @elseif(is_bool($nestedValue))
                                                                    <span class="px-2 py-1 text-xs font-medium rounded-full {{ $nestedValue ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                                                        {{ $nestedValue ? 'true' : 'false' }}
                                                                    </span>
                                                                @elseif(is_null($nestedValue))
                                                                    <span class="text-gray-400 italic">null</span>
                                                                @else
                                                                    {{ $nestedValue }}
                                                                @endif
                                                            </td>
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    </details>
                                @elseif(is_bool($value))
                                    <span class="px-2 py-1 text-xs font-medium rounded-full {{ $value ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                        {{ $value ? 'true' : 'false' }}
                                    </span>
                                @elseif(is_null($value))
                                    <span class="text-gray-400 italic">null</span>
                                @elseif(preg_match('/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/', $value))
                                    {{ \Carbon\Carbon::parse($value)->format('Y-m-d H:i:s') }}
                                @elseif(str_ends_with($key, '_id') || in_array($key, ['created_by', 'updated_by', 'deleted_by']))
                                    <div class="flex items-center">
                                        <span>{{ resolveIdToName($key, $value) }}</span>
                                        <span class="ml-2 text-xs text-gray-500">(ID: {{ $value }})</span>
                                    </div>
                                @else
                                    {{ $value }}
                                @endif
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
=======
        <div class="space-y-8">
            @foreach($sectionOrder as $section)
                @if(!empty($grouped[$section]))
                    <div class="pb-6 @if(!$loop->last) border-b border-gray-200 dark:border-gray-700 @endif">
                        <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">{{ $section }}</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            @foreach($grouped[$section] as $key => $value)
                                <div class="mb-3">
                                    <div class="text-sm font-semibold text-gray-700 dark:text-gray-300">
                                        {{ str_ends_with($key, '_id') ? str_replace('_id', '', ucfirst(str_replace('_', ' ', $key))) : ucfirst(str_replace('_', ' ', $key)) }}
                                    </div>
                                    <div class="text-sm text-gray-900 dark:text-gray-100 mt-1">
                                        @if(is_array($value) || is_object($value))
                                            <details class="text-sm">
                                                <summary class="cursor-pointer text-primary-600 hover:text-primary-500">
                                                    {{ is_array($value) ? 'Array' : 'Object' }} ({{ count((array)$value) }} items)
                                                </summary>
                                                <div class="mt-2 pl-4 border-l-2 border-gray-200 dark:border-gray-700">
                                                    <table class="w-full text-sm">
                                                        <tbody>
                                                        @foreach((array)$value as $nestedKey => $nestedValue)
                                                            <tr class="border-t dark:border-gray-700">
                                                                <td class="py-2 px-4 font-medium text-gray-700 dark:text-gray-200 bg-gray-50 dark:bg-gray-700">{{ $nestedKey }}</td>
                                                                <td class="py-2 px-4 dark:text-gray-200">
                                                                    @if(is_array($nestedValue) || is_object($nestedValue))
                                                                        <code class="text-xs bg-gray-100 dark:bg-gray-700 p-1 rounded">{{ json_encode($nestedValue, JSON_PRETTY_PRINT) }}</code>
                                                                    @elseif(is_bool($nestedValue))
                                                                        <span class="px-2 py-1 text-xs font-medium rounded-full {{ $nestedValue ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                                                            {{ $nestedValue ? 'true' : 'false' }}
                                                                        </span>
                                                                    @elseif(is_null($nestedValue))
                                                                        <span class="text-gray-400 italic">null</span>
                                                                    @else
                                                                        {{ $nestedValue }}
                                                                    @endif
                                                                </td>
                                                            </tr>
                                                        @endforeach
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </details>
                                        @elseif(is_bool($value))
                                            <span class="px-2 py-1 text-xs font-medium rounded-full {{ $value ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                            {{ $value ? 'true' : 'false' }}
                                        </span>
                                        @elseif(is_null($value))
                                            <span class="text-gray-400 italic">null</span>
                                        @elseif(preg_match('/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/', $value))
                                            {{ \Carbon\Carbon::parse($value)->format('Y-m-d H:i:s') }}
                                        @elseif(str_ends_with($key, '_id') || in_array($key, ['created_by', 'updated_by', 'deleted_by']))
                                            <div class="flex items-center">
                                                <span>{{ resolveIdToName($key, $value) }}</span>
                                                <span class="ml-2 text-xs text-gray-500">(ID: {{ $value }})</span>
                                            </div>
                                        @else
                                            {{ $value }}
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif
            @endforeach
>>>>>>> Stashed changes
        </div>
    @elseif(is_null($data))
        <div class="p-2 text-gray-500 dark:text-gray-400 italic">No data available</div>
    @else
        <div class="p-2 dark:text-gray-200">{{ $data }}</div>
    @endif
</div>