services:
  _defaults:
    public: true

  prestashop.core.grid.action.row.accessibility_checker.delete_profile:
    class: 'PrestaShop\PrestaShop\Core\Grid\Action\Row\AccessibilityChecker\DeleteProfileAccessibilityChecker'
    arguments:
      - '@=service("prestashop.adapter.legacy.configuration").get("_PS_ADMIN_PROFILE_")'

  prestashop.core.grid.action.row.accessibility_checker.print_invoice:
    class: 'PrestaShop\PrestaShop\Core\Grid\Action\Row\AccessibilityChecker\PrintInvoiceAccessibilityChecker'

  prestashop.core.grid.action.row.accessibility_checker.print_delivery_slip:
    class: 'PrestaShop\PrestaShop\Core\Grid\Action\Row\AccessibilityChecker\PrintDeliverySlipAccessibilityChecker'

  prestashop.core.grid.action.row.accessibility_checker.delete_order_return_states:
    class: 'PrestaShop\PrestaShop\Core\Grid\Action\Row\AccessibilityChecker\DeleteOrderReturnStatesAccessibilityChecker'

  prestashop.core.grid.action.row.accessibility_checker.delete_order_states:
    class: 'PrestaShop\PrestaShop\Core\Grid\Action\Row\AccessibilityChecker\DeleteOrderStatesAccessibilityChecker'

  PrestaShop\PrestaShop\Core\Grid\Action\Row\AccessibilityChecker\ProductSingleShopAssociatedAccessibilityChecker:
    autowire: true
    public: false

  PrestaShop\PrestaShop\Core\Grid\Action\Row\AccessibilityChecker\ProductMultipleShopsAssociatedAccessibilityChecker:
    autowire: true
    public: false
