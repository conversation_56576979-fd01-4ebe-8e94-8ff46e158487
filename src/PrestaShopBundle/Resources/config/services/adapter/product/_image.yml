services:
  _defaults:
    public: true

  PrestaShop\PrestaShop\Adapter\Product\Image\CommandHandler\AddProductImageHandler:
    autowire: true
    public: false
    tags:
      - name: tactician.handler
        command: PrestaShop\PrestaShop\Core\Domain\Product\Image\Command\AddProductImageCommand

  PrestaShop\PrestaShop\Adapter\Product\Image\CommandHandler\UpdateProductImageHandler:
    arguments:
      - '@PrestaShop\PrestaShop\Adapter\Product\Image\Repository\ProductImageRepository'
      - '@PrestaShop\PrestaShop\Adapter\Product\Image\Update\ProductImageUpdater'
      - '@PrestaShop\PrestaShop\Adapter\Product\Image\Uploader\ProductImageUploader'
      - '@PrestaShop\PrestaShop\Adapter\Image\ProductImageFileValidator'
    tags:
      - name: tactician.handler
        command: PrestaShop\PrestaShop\Core\Domain\Product\Image\Command\UpdateProductImageCommand

  PrestaShop\PrestaShop\Adapter\Product\Image\CommandHandler\DeleteProductImageHandler:
    arguments:
      - '@PrestaShop\PrestaShop\Adapter\Product\Image\Update\ProductImageUpdater'
    tags:
      - name: tactician.handler
        command: PrestaShop\PrestaShop\Core\Domain\Product\Image\Command\DeleteProductImageCommand

  PrestaShop\PrestaShop\Adapter\Product\Image\CommandHandler\SetProductImagesForAllShopHandler:
    autowire: true
    public: false
    tags:
      - name: tactician.handler
        command: PrestaShop\PrestaShop\Core\Domain\Product\Image\Command\SetProductImagesForAllShopCommand

  PrestaShop\PrestaShop\Adapter\Product\Image\QueryHandler\GetProductImagesHandler:
    autowire: true
    public: false
    tags:
      - name: tactician.handler
        command: PrestaShop\PrestaShop\Core\Domain\Product\Image\Query\GetProductImages

  PrestaShop\PrestaShop\Adapter\Product\Image\QueryHandler\GetShopProductImagesHandler:
    autowire: true
    public: false
    tags:
      - name: tactician.handler
        command: PrestaShop\PrestaShop\Core\Domain\Product\Image\Query\GetShopProductImages

  PrestaShop\PrestaShop\Adapter\Product\Image\QueryHandler\GetProductImageHandler:
    autowire: true
    public: false
    tags:
      - name: tactician.handler
        command: PrestaShop\PrestaShop\Core\Domain\Product\Image\Query\GetProductImage

  PrestaShop\PrestaShop\Adapter\Product\Image\Validate\ProductImageValidator: ~

  PrestaShop\PrestaShop\Adapter\Product\Image\Repository\ProductImageRepository:
    autowire: true
    arguments:
      - '@doctrine.dbal.default_connection'
      - '%database_prefix%'

  PrestaShop\PrestaShop\Adapter\Product\Image\Update\ProductImageUpdater:
    arguments:
      - '@PrestaShop\PrestaShop\Adapter\Product\Image\Uploader\ProductImageUploader'
      - '@PrestaShop\PrestaShop\Core\Grid\Position\PositionUpdateFactoryInterface'
      - '@prestashop.core.grid.image.position_definition'
      - '@PrestaShop\PrestaShop\Core\Grid\Position\GridPositionUpdaterInterface'
      - '@PrestaShop\PrestaShop\Adapter\Product\Image\Repository\ProductImageRepository'

  PrestaShop\PrestaShop\Adapter\Product\Image\ProductImagePathFactory:
    public: true
    arguments:
      - '@=service("prestashop.adapter.shop.url.product_image_folder_provider").getUrl()'
      - !php/const _PS_TMP_IMG_DIR_
      - '@=service("prestashop.adapter.legacy.context").getContext().language.iso_code'

  PrestaShop\PrestaShop\Core\Domain\Product\Image\Provider\ProductImageProviderInterface: '@PrestaShop\PrestaShop\Adapter\Product\Image\ProductImageProvider'

  PrestaShop\PrestaShop\Adapter\Product\Image\ProductImageProvider:
    public: false
    autowire: true

  # This is an alternative service that also generates product image path, but it returns absolute file path not urls
  prestashop.adapter.product.image.product_image_filesystem_path_factory:
    class: PrestaShop\PrestaShop\Adapter\Product\Image\ProductImagePathFactory
    arguments:
      - !php/const _PS_PRODUCT_IMG_DIR_
      - !php/const _PS_TMP_IMG_DIR_
      - '@=service("prestashop.adapter.legacy.context").getContext().language.iso_code'

  PrestaShop\PrestaShop\Adapter\Product\Image\Uploader\ProductImageUploader:
    autowire: true
    public: false
    arguments:
      $productImagePathFactory: '@prestashop.adapter.product.image.product_image_filesystem_path_factory'
      $contextShopId: "@=service('prestashop.adapter.legacy.context').getContext().shop.id"
