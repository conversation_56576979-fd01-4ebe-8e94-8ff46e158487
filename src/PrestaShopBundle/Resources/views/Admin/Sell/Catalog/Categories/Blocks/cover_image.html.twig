{#**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 *#}

{% block category_cover_image %}
  {% if coverImage is defined and coverImage is not null %}
    <div>
      <form action="">
        <figure class="figure">
          <img src="{{ coverImage.path }}" class="figure-img img-fluid img-thumbnail">
          <figcaption class="figure-caption">
            <p>{{ 'File size'|trans({}, 'Admin.Advparameters.Feature') }} {{ coverImage.size }}</p>
            <button class="btn btn-outline-danger btn-sm js-form-submit-btn"
                    data-form-submit-url="{{ path('admin_categories_delete_cover_image', {'categoryId': app.request.get('categoryId')}) }}"
                    data-form-csrf-token="{{ csrf_token('delete-cover-image') }}"
                    type="button"
            >
              <i class="material-icons">
                delete_forever
              </i>
              {{ 'Delete'|trans({}, 'Admin.Actions') }}
            </button>
          </figcaption>
        </figure>
      </form>
    </div>
  {% endif %}
{% endblock %}
