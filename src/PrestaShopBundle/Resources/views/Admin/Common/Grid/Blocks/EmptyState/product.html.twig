{#**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 *#}

<div class="text-center showcase-list-card">
  <img class="img-responsive mt-3 img-rtl" src="{{ asset('themes/new-theme/img/empty_state/catalog.png') }}">

  <p class="mt-4 showcase-list-card__header">{{ 'Manage your products'|trans({}, 'Admin.Catalog.Feature') }}</p>

  <div class="mt-4">
    <a href="{{ path('admin_products_create') }}" class="btn btn-primary ml-1 new-product-button">
      {{ 'Create new product'|trans({}, 'Admin.Catalog.Feature') }}
    </a>
    {{ 'or'|trans({}, 'Admin.Global') }}
    <a href="{{ path('admin_import') }}" target="_blank" class="btn btn-outline-secondary mr-1">
      {{ 'Import a list of products.'|trans({}, 'Admin.Catalog.Feature') }}
    </a>
  </div>
</div>
