{#**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 *#}
{% if previous_url or next_url %}
<nav>
    <ul class="pagination">
        <li class="page-item {% if first_url %}{% else %}disabled{% endif %}">
            <a class="page-link" {% if first_url %}href="{{ first_url }}"{% else %}nohref{% endif %}>1</a>
        </li>
        <li class="page-item {% if previous_url %}{% else %}disabled{% endif %}">
            <a class="page-link" data-role="previous-page-link" {% if previous_url %}href="{{ previous_url }}"{% else %}nohref{% endif %}>&lt;</a>
        </li>
        <li class="page-item active" style="float: left;">
            <input name="paginator_jump_page" class="jump-to-page" type="text" style="width: 4em;"
                   value="{{ current_page }}" psurl="{{ jump_page_url }}" psmax="{{ page_count }}" pslimit="{{ limit }}" />
        </li>
        <li class="page-item {% if next_url is defined and next_url != false %}{% else %}disabled{% endif %}">
            <a class="page-link" data-role="next-page-link" id="pagination_next_url" {% if next_url is defined and next_url != false %}href="{{ next_url }}"{% else %}nohref{% endif %}>&gt;</a>
        </li>
        <li class="page-item {% if last_url is defined and last_url != false %}{% else %}disabled{% endif %}">
            <a class="page-link" {% if last_url is defined and last_url != false %}href="{{ last_url }}"{% else %}nohref{% endif %}>{{ page_count }}</a>
        </li>
    </ul>
</nav>
{% endif %}
