admin_sql_requests_index:
  path: /
  methods: [ GET ]
  defaults:
    _controller: 'PrestaShopBundle\Controller\Admin\Configure\AdvancedParameters\SqlManagerController::indexAction'
    _legacy_controller: AdminRequestSql
    _legacy_link: AdminRequestSql

admin_sql_requests_search:
  path: /
  methods: [ POST ]
  defaults:
    _controller: PrestaShopBundle\Controller\Admin\CommonController::searchGridAction
    gridDefinitionFactoryServiceId: prestashop.core.grid.definition.factory.request_sql
    redirectRoute: admin_sql_requests_index
    _legacy_controller: AdminRequestSql
    _legacy_link: AdminRequestSql:search

admin_sql_requests_process_settings:
  path: /process-settings
  methods: [ POST ]
  defaults:
    _controller: 'PrestaShopBundle\Controller\Admin\Configure\AdvancedParameters\SqlManagerController::processFormAction'
    _legacy_controller: AdminRequestSql
    _legacy_link: AdminRequestSql:update

admin_sql_requests_create:
  path: /new
  methods: [ GET, POST ]
  defaults:
    _controller: 'PrestaShopBundle\Controller\Admin\Configure\AdvancedParameters\SqlManagerController::createAction'
    _legacy_controller: AdminRequestSql
    _legacy_link: AdminRequestSql:addrequest_sql

admin_sql_requests_edit:
  path: /{sqlRequestId}/edit
  methods: [ GET, POST ]
  defaults:
    _controller: 'PrestaShopBundle\Controller\Admin\Configure\AdvancedParameters\SqlManagerController::editAction'
    _legacy_controller: AdminRequestSql
    _legacy_link: AdminRequestSql:updaterequest_sql
    _legacy_parameters:
      id_request_sql: sqlRequestId
  requirements:
    sqlRequestId: \d+

admin_sql_requests_delete:
  path: /{sqlRequestId}/delete
  methods: [ GET, DELETE ]
  defaults:
    _controller: 'PrestaShopBundle\Controller\Admin\Configure\AdvancedParameters\SqlManagerController::deleteAction'
    _legacy_controller: AdminRequestSql
    _legacy_link: AdminRequestSql:deleterequest_sql
    _legacy_parameters:
      id_request_sql: sqlRequestId
  requirements:
    sqlRequestId: \d+

admin_sql_requests_delete_bulk:
  path: /delete-bulk
  methods: [ POST ]
  defaults:
    _controller: 'PrestaShopBundle\Controller\Admin\Configure\AdvancedParameters\SqlManagerController::deleteBulkAction'
    _legacy_controller: AdminRequestSql
    _legacy_link: AdminRequestSql:submitBulkdeleterequest_sql

admin_sql_requests_table_columns:
  path: /tables/{mySqlTableName}/columns
  methods: [ GET ]
  defaults:
    _controller: 'PrestaShopBundle\Controller\Admin\Configure\AdvancedParameters\SqlManagerController::ajaxTableColumnsAction'
    _legacy_controller: AdminRequestSql
    _legacy_link: AdminRequestSql:ajax
    _legacy_parameters:
      table: mySqlTableName

admin_sql_requests_view:
  path: /{sqlRequestId}/view
  methods: [ GET ]
  defaults:
    _controller: 'PrestaShopBundle\Controller\Admin\Configure\AdvancedParameters\SqlManagerController::viewAction'
    _legacy_controller: AdminRequestSql
    _legacy_link: AdminRequestSql:viewsql_request
    _legacy_parameters:
      id_request_sql: sqlRequestId
  requirements:
    sqlRequestId: \d+

admin_sql_requests_export:
  path: /{sqlRequestId}/export
  methods: [ GET ]
  defaults:
    _controller: 'PrestaShopBundle\Controller\Admin\Configure\AdvancedParameters\SqlManagerController::exportAction'
    _legacy_controller: AdminRequestSql
    _legacy_link: AdminRequestSql:exportsql_request
    _legacy_parameters:
      id_request_sql: sqlRequestId
  requirements:
    sqlRequestId: \d+
