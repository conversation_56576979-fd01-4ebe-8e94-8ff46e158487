admin_preferences:
  path: preferences
  methods: [ GET ]
  defaults:
    _controller: 'PrestaShopBundle\Controller\Admin\Configure\ShopParameters\PreferencesController::indexAction'
    _legacy_controller: AdminPreferences
    _legacy_link: AdminPreferences

admin_preferences_save:
  path: preferences
  methods: [ POST ]
  defaults:
    _controller: 'PrestaShopBundle\Controller\Admin\Configure\ShopParameters\PreferencesController::processFormAction'
    _legacy_controller: AdminPreferences
    _legacy_link: AdminPreferences:update
