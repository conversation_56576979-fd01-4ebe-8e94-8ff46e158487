services:
  _defaults:
    public: true

  prestashop.core.currency.grid_data_factory:
    class: 'PrestaShop\PrestaShop\Core\Currency\CurrencyGridDataFactory'
    arguments:
      - '@prestashop.core.grid.data_provider.currency'

  prestashop.core.currency.exchange_rate.settings:
    class: PrestaShop\CircuitBreaker\FactorySettings
    arguments:
      - !php/const \PrestaShop\PrestaShop\Core\Currency\ExchangeRateProvider::CLOSED_ALLOWED_FAILURES
      - !php/const \PrestaShop\PrestaShop\Core\Currency\ExchangeRateProvider::CLOSED_TIMEOUT_SECONDS
      - !php/const \PrestaShop\PrestaShop\Core\Currency\ExchangeRateProvider::OPEN_THRESHOLD_SECONDS
    calls:
      - [ 'setStrippedFailures', [ !php/const \PrestaShop\PrestaShop\Core\Currency\ExchangeRateProvider::OPEN_ALLOWED_FAILURES ] ]
      - [ 'setStrippedTimeout', [ !php/const \PrestaShop\PrestaShop\Core\Currency\ExchangeRateProvider::OPEN_TIMEOUT_SECONDS ] ]
      - [ 'setStorage', [ '@prestashop.core.circuit_breaker.storage' ] ]
      # create a factory for cache_subscriber so that we can easily adapt the cache duration

  prestashop.core.currency.exchange_rate.circuit_breaker:
    class: 'PrestaShop\CircuitBreaker\Contract\CircuitBreakerInterface'
    factory: [ "@prestashop.core.circuit_breaker.advanced_factory", create ]
    arguments:
      - "@prestashop.core.currency.exchange_rate.settings"

  prestashop.core.exchange_rate.provider:
    class: PrestaShop\PrestaShop\Core\Currency\ExchangeRateProvider
    arguments:
      - !php/const \PrestaShop\PrestaShop\Core\Currency\ExchangeRateProvider::CURRENCY_FEED_URL
      - '@=service("prestashop.adapter.data_provider.currency").getDefaultCurrencyIsoCode()'
      - '@prestashop.core.currency.exchange_rate.circuit_breaker'
      - '@cache.app'

  prestashop.core.localization.currency.pattern_transformer:
    class: PrestaShop\PrestaShop\Core\Localization\Currency\PatternTransformer
