<?php
/**
 * OptionsProcessor.php
 *
 * This file implements the processor for the statement options.
 *
 * PHP version 5
 *
 * LICENSE:
 * Copyright (c) 2010-2014 <PERSON> and <PERSON>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 * 3. The name of the author may not be used to endorse or promote products
 *    derived from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 * IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT,
 * INCIDENT<PERSON>, <PERSON>ECIAL, EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UE<PERSON><PERSON><PERSON> DAMAGES (INCLUDING, BUT
 * NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
 * THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 * <AUTHOR> Rothe <<EMAIL>>
 * @copyright 2010-2014 Justin Swanhart and André Rothe
 * @license   http://www.debian.org/misc/bsd.license  BSD License (3 Clause)
 * @version   SVN: $Id$
 *
 */

namespace PHPSQLParser\processors;
use PHPSQLParser\utils\ExpressionType;

/**
 * This class processes the statement options.
 *
 * <AUTHOR> Rothe <<EMAIL>>
 * @license http://www.debian.org/misc/bsd.license  BSD License (3 Clause)
 *
 */
class OptionsProcessor extends AbstractProcessor {

    public function process($tokens) {
        $resultList = array();

        foreach ($tokens as $token) {

            $tokenList = $this->splitSQLIntoTokens($token);
            $result = array();

            foreach ($tokenList as $reserved) {
                $trim = trim($reserved);
                if ($trim === '') {
                    continue;
                }
                $result[] = array('expr_type' => ExpressionType::RESERVED, 'base_expr' => $trim);
            }
            $resultList[] = array('expr_type' => ExpressionType::EXPRESSION, 'base_expr' => trim($token),
                                  'sub_tree' => $result);
        }

        return $resultList;
    }
}

?>
