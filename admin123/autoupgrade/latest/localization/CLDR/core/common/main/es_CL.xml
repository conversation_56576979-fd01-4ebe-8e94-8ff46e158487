<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE ldml SYSTEM "../../common/dtd/ldml.dtd">
<!-- Copyright © 1991-2020 Unicode, Inc.
For terms of use, see http://www.unicode.org/copyright.html
Unicode and the Unicode Logo are registered trademarks of Unicode, Inc. in the U.S. and other countries.
CLDR data files are interpreted according to the LDML specification (http://unicode.org/reports/tr35/)
-->
<ldml>
	<identity>
		<version number="$Revision$"/>
		<language type="es"/>
		<territory type="CL"/>
	</identity>
	<localeDisplayNames>
		<languages>
			<language type="ace" draft="contributed">acehnés</language>
			<language type="arp" draft="contributed">arapaho</language>
			<language type="bho" draft="contributed">bhojpuri</language>
			<language type="eu" draft="contributed">euskera</language>
			<language type="grc" draft="contributed">griego antiguo</language>
			<language type="lo" draft="contributed">lao</language>
			<language type="nso" draft="contributed">sotho septentrional</language>
			<language type="pa" draft="contributed">punyabí</language>
			<language type="ss" draft="contributed">siswati</language>
			<language type="sw" draft="contributed">suajili</language>
			<language type="sw_CD" draft="contributed">suajili del Congo</language>
			<language type="tn" draft="contributed">setswana</language>
			<language type="wo" draft="contributed">wolof</language>
			<language type="zgh" draft="contributed">tamazight marroquí estándar</language>
		</languages>
		<territories>
			<territory type="BA" draft="contributed">Bosnia y Herzegovina</territory>
			<territory type="EH">Sahara Occidental</territory>
			<territory type="GB" alt="short" draft="contributed">RU</territory>
			<territory type="PS" draft="unconfirmed">Territorio Palestino</territory>
			<territory type="TA" draft="contributed">Tristán de Acuña</territory>
			<territory type="UM" draft="contributed">Islas menores alejadas de EE. UU.</territory>
		</territories>
		<keys>
			<key type="currency" draft="unconfirmed">divisa</key>
		</keys>
		<types>
			<type key="collation" type="phonebook">orden de directorio telefónico</type>
		</types>
	</localeDisplayNames>
	<dates>
		<calendars>
			<calendar type="generic">
				<dateFormats>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern>dd-MM-y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern>dd-MM-y GGGGG</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
				<dateTimeFormats>
					<availableFormats>
						<dateFormatItem id="Md">dd-MM</dateFormatItem>
						<dateFormatItem id="MEd">E, dd-MM</dateFormatItem>
						<dateFormatItem id="yyyyM">MM-y G</dateFormatItem>
						<dateFormatItem id="yyyyMd">dd-MM-y G</dateFormatItem>
						<dateFormatItem id="yyyyMEd">E dd-MM-y G</dateFormatItem>
					</availableFormats>
					<intervalFormats>
						<intervalFormatFallback draft="contributed">{0} a el {1}</intervalFormatFallback>
						<intervalFormatItem id="H">
							<greatestDifference id="H" draft="unconfirmed">H–H</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hm">
							<greatestDifference id="h" draft="unconfirmed">h:mm–h:mm a</greatestDifference>
							<greatestDifference id="m" draft="unconfirmed">h:mm–h:mm a</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hm">
							<greatestDifference id="H" draft="unconfirmed">H:mm–H:mm</greatestDifference>
							<greatestDifference id="m" draft="unconfirmed">H:mm–H:mm</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hmv">
							<greatestDifference id="h" draft="unconfirmed">h:mm–h:mm a v</greatestDifference>
							<greatestDifference id="m" draft="unconfirmed">h:mm–h:mm a v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hmv">
							<greatestDifference id="H" draft="unconfirmed">H:mm–H:mm v</greatestDifference>
							<greatestDifference id="m" draft="unconfirmed">H:mm–H:mm v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hv">
							<greatestDifference id="H" draft="unconfirmed">H–H v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Md">
							<greatestDifference id="d" draft="contributed">dd-MM – dd-MM</greatestDifference>
							<greatestDifference id="M" draft="contributed">dd-MM – dd-MM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MEd">
							<greatestDifference id="d" draft="contributed">E dd-MM – E dd-MM</greatestDifference>
							<greatestDifference id="M" draft="contributed">E dd-MM – E dd-MM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMd">
							<greatestDifference id="M" draft="contributed">d 'de' MMM 'al' d 'de' MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMEd">
							<greatestDifference id="d" draft="contributed">E d 'al' E d 'de' MMM</greatestDifference>
							<greatestDifference id="M" draft="contributed">E d 'de' MMM 'al' E d 'de' MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yM">
							<greatestDifference id="M" draft="contributed">MM-y – MM-y G</greatestDifference>
							<greatestDifference id="y" draft="contributed">MM-y – MM-y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMd">
							<greatestDifference id="d" draft="contributed">dd-MM-y – dd-MM-y G</greatestDifference>
							<greatestDifference id="M" draft="contributed">dd-MM-y – dd-MM-y G</greatestDifference>
							<greatestDifference id="y" draft="contributed">dd-MM-y – dd-MM-y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMEd">
							<greatestDifference id="d" draft="contributed">E dd-MM-y – E dd-MM-y G</greatestDifference>
							<greatestDifference id="M" draft="contributed">E dd-MM-y – E dd-MM-y G</greatestDifference>
							<greatestDifference id="y" draft="contributed">E dd-MM-y – E dd-MM-y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMM">
							<greatestDifference id="y" draft="contributed">MMM 'de' y 'a' MMM 'de' y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMd">
							<greatestDifference id="M" draft="contributed">d 'de' MMM 'al' d 'de' MMM 'de' y G</greatestDifference>
							<greatestDifference id="y" draft="contributed">d 'de' MMM 'de' y 'al' d 'de' MMM 'de' y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMEd">
							<greatestDifference id="d" draft="contributed">E d 'al' E d 'de' MMM 'de' y G</greatestDifference>
							<greatestDifference id="M" draft="contributed">E d 'de' MMM 'al' E d 'de' MMM 'de' y G</greatestDifference>
							<greatestDifference id="y" draft="contributed">E d 'de' MMM 'de' y 'al' E d 'de' MMM 'de' y G</greatestDifference>
						</intervalFormatItem>
					</intervalFormats>
				</dateTimeFormats>
			</calendar>
			<calendar type="gregorian">
				<months>
					<monthContext type="stand-alone">
						<monthWidth type="abbreviated">
							<month type="1" draft="contributed">ene.</month>
							<month type="2" draft="contributed">feb.</month>
							<month type="3" draft="contributed">mar.</month>
							<month type="4" draft="contributed">abr.</month>
							<month type="5" draft="contributed">may.</month>
							<month type="6" draft="contributed">jun.</month>
							<month type="7" draft="contributed">jul.</month>
							<month type="8" draft="contributed">ago.</month>
							<month type="9" draft="contributed">sept.</month>
							<month type="10" draft="contributed">oct.</month>
							<month type="11" draft="contributed">nov.</month>
							<month type="12" draft="contributed">dic.</month>
						</monthWidth>
					</monthContext>
				</months>
				<days>
					<dayContext type="format">
						<dayWidth type="short">
							<day type="sun" draft="contributed">do</day>
							<day type="mon" draft="contributed">lu</day>
							<day type="tue" draft="contributed">ma</day>
							<day type="wed" draft="contributed">mi</day>
							<day type="thu" draft="contributed">ju</day>
							<day type="fri" draft="contributed">vi</day>
							<day type="sat" draft="contributed">sá</day>
						</dayWidth>
					</dayContext>
				</days>
				<quarters>
					<quarterContext type="stand-alone">
						<quarterWidth type="wide">
							<quarter type="1">1.° trimestre</quarter>
							<quarter type="2">2.° trimestre</quarter>
							<quarter type="3">3.° trimestre</quarter>
							<quarter type="4">4.º trimestre</quarter>
						</quarterWidth>
					</quarterContext>
				</quarters>
				<dateFormats>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern>dd-MM-y</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern>dd-MM-yy</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
				<dateTimeFormats>
					<availableFormats>
						<dateFormatItem id="GyMMMd" draft="contributed">d MMM y G</dateFormatItem>
						<dateFormatItem id="Md">dd-MM</dateFormatItem>
						<dateFormatItem id="MEd">E, dd-MM</dateFormatItem>
						<dateFormatItem id="yM">MM-y</dateFormatItem>
						<dateFormatItem id="yMd">dd-MM-y</dateFormatItem>
						<dateFormatItem id="yMEd">E dd-MM-y</dateFormatItem>
					</availableFormats>
					<intervalFormats>
						<intervalFormatFallback draft="contributed">{0} a el {1}</intervalFormatFallback>
						<intervalFormatItem id="hm">
							<greatestDifference id="h" draft="contributed">h:mm–h:mm a</greatestDifference>
							<greatestDifference id="m" draft="contributed">h:mm–h:mm a</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Md">
							<greatestDifference id="d" draft="contributed">dd-MM – dd-MM</greatestDifference>
							<greatestDifference id="M" draft="contributed">dd-MM – dd-MM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MEd">
							<greatestDifference id="d" draft="contributed">E dd-MM – E dd-MM</greatestDifference>
							<greatestDifference id="M" draft="contributed">E dd-MM – E dd-MM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMd">
							<greatestDifference id="M" draft="contributed">d 'de' MMM 'al' d 'de' MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMEd">
							<greatestDifference id="d" draft="contributed">E d 'al' E d 'de' MMM</greatestDifference>
							<greatestDifference id="M" draft="contributed">E d 'de' MMM 'al' E d 'de' MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yM">
							<greatestDifference id="M" draft="contributed">MM-y – MM-y</greatestDifference>
							<greatestDifference id="y" draft="contributed">MM-y – MM-y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMd">
							<greatestDifference id="d" draft="contributed">dd-MM-y – dd-MM-y</greatestDifference>
							<greatestDifference id="M" draft="contributed">dd-MM-y – dd-MM-y</greatestDifference>
							<greatestDifference id="y" draft="contributed">dd-MM-y – dd-MM-y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMEd">
							<greatestDifference id="d" draft="contributed">E dd-MM-y – E dd-MM-y</greatestDifference>
							<greatestDifference id="M" draft="contributed">E dd-MM-y – E dd-MM-y</greatestDifference>
							<greatestDifference id="y" draft="unconfirmed">E dd-MM-y – E dd-MM-y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMM">
							<greatestDifference id="y" draft="contributed">MMM 'de' y 'a' MMM 'de' y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMd">
							<greatestDifference id="M" draft="contributed">d 'de' MMM 'al' d 'de' MMM 'de' y</greatestDifference>
							<greatestDifference id="y" draft="contributed">d 'de' MMM 'de' y 'al' d 'de' MMM 'de' y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMEd">
							<greatestDifference id="d" draft="contributed">E d 'al' E d 'de' MMM 'de' y</greatestDifference>
							<greatestDifference id="M" draft="contributed">E d 'de' MMM 'al' E d 'de' MMM 'de' y</greatestDifference>
							<greatestDifference id="y" draft="contributed">E d 'de' MMM 'de' y 'al' E d 'de' MMM 'de' y</greatestDifference>
						</intervalFormatItem>
					</intervalFormats>
				</dateTimeFormats>
			</calendar>
		</calendars>
		<timeZoneNames>
			<metazone type="Chile">
				<short>
					<generic>CLT</generic>
					<standard>CLT</standard>
					<daylight>CLST</daylight>
				</short>
			</metazone>
		</timeZoneNames>
	</dates>
	<numbers>
		<symbols numberSystem="latn">
			<decimal>,</decimal>
			<group>.</group>
		</symbols>
		<currencyFormats numberSystem="latn">
			<currencyFormatLength>
				<currencyFormat type="standard">
					<pattern>¤#,##0.00;¤-#,##0.00</pattern>
				</currencyFormat>
			</currencyFormatLength>
		</currencyFormats>
		<currencies>
			<currency type="CLP">
				<displayName draft="contributed">Peso chileno</displayName>
				<symbol>$</symbol>
			</currency>
			<currency type="USD">
				<symbol>US$</symbol>
			</currency>
		</currencies>
	</numbers>
	<units>
		<unitLength type="long">
			<unit type="electric-ampere">
				<displayName draft="contributed">amperios</displayName>
				<unitPattern count="one" draft="contributed">{0} amperio</unitPattern>
				<unitPattern count="other" draft="contributed">{0} amperios</unitPattern>
			</unit>
			<unit type="electric-milliampere">
				<displayName draft="contributed">miliamperios</displayName>
				<unitPattern count="one" draft="contributed">{0} miliamperio</unitPattern>
				<unitPattern count="other" draft="contributed">{0} miliamperios</unitPattern>
			</unit>
			<unit type="electric-ohm">
				<unitPattern count="one">{0} ohmio</unitPattern>
				<unitPattern count="other">{0} ohmios</unitPattern>
			</unit>
		</unitLength>
		<unitLength type="short">
			<unit type="electric-volt">
				<displayName draft="contributed">voltios</displayName>
			</unit>
			<unit type="power-watt">
				<displayName draft="contributed">vatios</displayName>
			</unit>
		</unitLength>
		<durationUnit type="hms">
			<durationUnitPattern draft="contributed">hh:mm:ss</durationUnitPattern>
		</durationUnit>
		<durationUnit type="ms">
			<durationUnitPattern draft="contributed">mm:ss</durationUnitPattern>
		</durationUnit>
	</units>
</ldml>
