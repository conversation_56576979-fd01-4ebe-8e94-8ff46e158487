<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("ALTER TABLE notification_types MODIFY COLUMN event ENUM('account_created', 'account_locked', 'account_reactivated', 'account_role_changed', 'account_password_reset', 'account_invitation_sent', 'account_otp_sent', 'assigned_license', 'provided_license', 'receipt_approved', 'receipt_rejected', 'subscription_activated', 'license_requested')");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement("ALTER TABLE notification_types MODIFY COLUMN event ENUM('account_created', 'account_locked', 'account_reactivated', 'account_role_changed', 'account_password_reset', 'account_invitation_sent', 'account_otp_sent', 'assigned_license', 'provided_license', 'receipt_approved', 'receipt_rejected', 'subscription_activated')");
    }
};
