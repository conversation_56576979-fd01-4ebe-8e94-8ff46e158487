<?php

namespace Database\Factories;

use App\Models\Category;
use App\Models\Worker;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class ItemFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $statuses = ['active', 'inactive', 'discontinued'];
        $is_service = $this->faker->boolean(50);
        
        return [
            'name' => [
                'en' => $this->faker->words(3, true),
                'ar' => $this->faker->words(3, true) . ' (عربي)'
            ],
            'description' => [
                'en' => $this->faker->sentence(),
                'ar' => $this->faker->sentence() . ' (عربي)'
            ],
            'category_id' => Category::inRandomOrder()->first()?->id ?? Category::factory(),
            'worker_id' => $type === 'service' ? (Worker::inRandomOrder()->first()?->id ?? Worker::factory()) : null,
            'price' => $this->faker->randomFloat(2, 10, 1000),
            'cost' => $this->faker->boolean(70) ? $this->faker->randomFloat(2, 5, 800) : null,
            'status' => $this->faker->randomElement($statuses),
            'stock_quantity' => $type === 'product' ? $this->faker->numberBetween(0, 100) : 0,
            'low_stock_threshold' => $this->faker->numberBetween(5, 20),
            'sku' => 'ITM-' . strtoupper(Str::random(8)),
            'barcode' => $this->faker->isbn13,
            'images' => $this->faker->boolean(30) ? [
                $this->faker->imageUrl(640, 480, 'product', true),
                $this->faker->imageUrl(640, 480, 'product', true),
            ] : null,
            'type' => $type,
            'is_active' => $this->faker->boolean(90), // 90% chance of being active
            'created_by' => User::inRandomOrder()->first()?->id ?? User::factory(),
            'created_at' => $this->faker->dateTimeBetween('-1 year', 'now'),
            'updated_at' => $this->faker->dateTimeBetween('-1 year', 'now'),
        ];
    }

    /**
     * Indicate that the item is a product.
     */
    public function product(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'product',
            'stock_quantity' => $this->faker->numberBetween(0, 100),
            'worker_id' => null,
        ]);
    }

    /**
     * Indicate that the item is a service.
     */
    public function service(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'service',
            'stock_quantity' => 0,
            'worker_id' => Worker::inRandomOrder()->first()?->id ?? Worker::factory(),
        ]);
    }

    /**
     * Indicate that the item is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
            'status' => 'active',
        ]);
    }

    /**
     * Indicate that the item is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
            'status' => 'inactive',
        ]);
    }

    /**
     * Indicate that the item is low in stock.
     */
    public function lowStock(): static
    {
        return $this->state(function (array $attributes) {
            $threshold = $this->faker->numberBetween(5, 10);
            return [
                'stock_quantity' => $this->faker->numberBetween(1, $threshold),
                'low_stock_threshold' => $threshold,
            ];
        });
    }

    /**
     * Indicate that the item is out of stock.
     */
    public function outOfStock(): static
    {
        return $this->state([
            'stock_quantity' => 0,
            'status' => 'out_of_stock',
        ]);
    }
}
