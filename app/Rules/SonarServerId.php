<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class SonarServerId implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    /**
     * Validate the given attribute.
     *
     * @param string $attribute The name of the attribute being validated.
     * @param mixed $value The value of the attribute.
     * @param \Closure $fail The callback to call on validation failure.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Always pass validation for now.
        // Original validation (commented out):
        // if (!preg_match('/^[A-Z0-9]{8}-[A-Za-z0-9]{20}$/', $value)) {
        //     $fail("Invalid Server ID format. Please enter a valid identifier.");
        // }
    }
}
