<?php

namespace App\Http\Controllers\Subscriptions\Api;

use App\Enums\ApiStatus;
use App\Enums\DefaultValues;
use App\Enums\Notifications;
use App\Enums\SubscriptionLicenses;
use App\Enums\SubscriptionMessages;
use App\Enums\Subscriptions;
use App\Enums\ValidationMessages;
use App\Helpers\ApiHelper;
use App\Helpers\AppHelper;
use App\Helpers\BillingsHelper;
use App\Helpers\CustomerHelper;
use App\Helpers\SubscriptionHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\CompanyAddressResource;
use App\Http\Resources\SubscriptionResource;
use App\Mail\LicenseRequestAcknowledgmentMail;
use App\Models\Company;
use App\Models\CompanyAddress;
use App\Models\Country;
use App\Models\Currency;
use App\Models\Plan;
use App\Models\PlanFeature;
use App\Models\PlatformCountry;
use App\Models\Subscription;
use App\Models\SubscriptionFeature;
use App\Models\SubscriptionLicense;
use App\Models\SubscriptionPayment;
use App\Models\SubscriptionUsage;
use App\Observers\SubscriptionLicenseObserver;
use App\Observers\SubscriptionObserver;
use App\Services\ApiResponseService;
use App\Services\GcsService;
use App\Services\LogService;
use App\Services\MoneyFormatter;
use App\Services\NotificationService;
use App\Services\SubscriptionProgressService;
use App\Services\SonarQubeService;
use App\Services\SonarQubeTokenService;
use App\Services\SubscriptionService;
use App\Traits\Blamable;
use Cknow\Money\Money;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Validator;

use function PHPUnit\Framework\isNumeric;

class SubscriptionsController extends Controller
{
    use Blamable;

    protected $apiResponse;

    protected $moneyFormatter;

    protected $GcsService;

    protected $logService;

    protected $subscriptionProgressService;

    public function __construct(ApiResponseService $apiResponse, MoneyFormatter $moneyFormatter, GcsService $GcsService, LogService $logService, SubscriptionProgressService $subscriptionProgressService)
    {
        $this->apiResponse = $apiResponse;
        $this->moneyFormatter = $moneyFormatter;
        $this->GcsService = $GcsService;
        $this->logService = $logService;
        $this->subscriptionProgressService = $subscriptionProgressService;
    }

    /**
     * List customers subscriptions 
     */
    public function list(Request $request, ?string $subscriptionId = null)
    {

        $user = CustomerHelper::getCompanyUsers($this->apiResponse, self::getBlamableId());

        if ($user instanceof \Illuminate\Http\JsonResponse) {
            return $user;
        }

        if (!empty($subscriptionId)) {
            return $this->apiResponse->props(ApiStatus::SUCCESS)
                ->withData((new SubscriptionResource(SubscriptionHelper::getSubscriptionsList($user->company_id, $subscriptionId)))->toArray($request))
                ->send();
        }

        return $this->apiResponse->props(ApiStatus::SUCCESS)
            ->withData(SubscriptionResource::collection(SubscriptionHelper::getSubscriptionsList($user->company_id))->toArray($request))
            ->send();
    }

    /**
     * New Subscribe Method 
     */
    public function Subscribe(Request $request)
    {
        $user = CustomerHelper::getCompanyUsers($this->apiResponse, self::getBlamableId());

        if ($user instanceof \Illuminate\Http\JsonResponse) {
            return $user;
        }

        $freeTrialPlanId = optional(Plan::where('slug', Subscriptions::TRIAL_VERSION->value)->first())->id;

        $validator = Validator::make($request->all(), [
            'step' => 'string|required',
            'plan_id' => 'integer|exists:plans,id',
            'feature_id' => 'integer|exists:plan_features,plan_feature_id',
            'billing_country_id' => 'integer|exists:countries,id|required_unless:plan_id,' . $freeTrialPlanId,
            'platform_country_id' => 'integer|exists:platform_countries,platform_country_id|required_unless:plan_id,' . $freeTrialPlanId,
            'addons' => 'array',
            'addons.*' => 'integer|exists:plan_features,plan_feature_id',
            'auto_renew' => 'integer',
            'number_of_years' => 'sometimes|integer|min:1',
            'payment_type' => 'string|in:wire_transfer,online_payment',
            'payment_gateway' => 'string|nullable',
            'platform_bank_id' => 'integer|exists:platform_banks,platform_bank_id',
            'billing_information' => 'array',
            'billing_information.city' => 'string|nullable',
            'billing_information.zip' => 'string|nullable',
            'billing_information.detailed_address' => 'string|nullable',
            'billing_information.tax_id' => 'string|nullable',
            'billing_information.currency' => 'string|nullable|exists:currencies,iso',
            'billing_information.company_registration_number' => 'string|nullable',
            'receipt_file.*' => 'file|mimes:jpeg,png,jpg,pdf|max:5120',
            'receipt_file' => 'array|min:1',
        ], [
            'billing_country_id.required_unless' => 'Billing country is required for non-trial plans.',
            'platform_country_id.required_unless' => 'Deployment country is required for non-trial plans.',
            'receipt_file.*.mimes' => SubscriptionMessages::SUBSCRIPTION_INVALID_RECEIPT->description(),
        ]);

        if ($validator->fails()) {
            return ApiHelper::validationError($this->apiResponse, $validator->errors()->toArray());
        }

        DB::beginTransaction();

        try {
            /**
             * Variables 
             */
            $step = $request->step;
            $customer = $user->company;
            $addons = $request->addons;
            $number_of_years = (int)$request->number_of_years ?? DefaultValues::SUBSCRIPTION_LIFETIME_YEARS->get();
            $number_of_years = DefaultValues::SUBSCRIPTION_LIFETIME_YEARS->get();

            if (!empty($request->number_of_years) && is_numeric($request->number_of_years) && $request->number_of_years > 0) {
                $number_of_years = (int)$request->number_of_years;
            }

            /**
             * ids 
             */
            $plan_id = $request->plan_id;
            $customer_id = $customer->company_id;

            /**
             * Models 
             */
            $planModel = Plan::enabled()->find($plan_id);
            $basePlanFeatureModel = PlanFeature::find($request->feature_id);
            $currencyModel = Currency::where('iso', $planModel->currency)->first();
            $subscriptionModel = $customer->planSubscriptions()->where('subscription_status', Subscriptions::SUBSCRIPTION_STATUS_INCOMPLETE->value)->first();

            /**
             * Booleans 
             */
            $isFreeTrial = $planModel->isFreeTrial();
            $isWireTransferPayment = AppHelper::matchStrings($request->payment_type, Subscriptions::PAYMENT_TYPE_WIRE_TRANSFER);

            /**
             * Skip Observer  
             */
            SubscriptionObserver::$skipObserver = true;

            /**
             * Step selected_plan 
             */
            if (AppHelper::matchStrings($step, Subscriptions::STEP_SELECTED_PLAN)) {

                /**
                 * Customer Has Already Benefit From Trial
                 */
                if ($isFreeTrial && $customer->hasPreviousSubscriptionsOfType()) {

                    DB::rollBack();

                    return $this->apiResponse
                        ->props(ApiStatus::BAD_REQUEST, SubscriptionMessages::SUBSCRIPTION_TRIAL_EXIST->description())
                        ->send();
                }

                /**
                 * Data 
                 */
                $data = [
                    'name' => config('settings.default_product_name'),
                    'plan_id' => $plan_id,
                    'country_id' => $request->platform_country_id,
                    'billing_country_id' => $request->billing_country_id,
                    'currency_id' => $currencyModel?->id,
                    'subscriber_type' => Company::class,
                    'subscriber_id' => $customer_id,
                    'subscription_status' => Subscriptions::SUBSCRIPTION_STATUS_INCOMPLETE->value,
                    'payment_status' => Subscriptions::SUBSCRIPTION_PAYMENT_STATUS_PENDING->value,
                    'timezone' => $customer->timezone,
                    'starts_at' => now(),
                    'ends_at' => now()->addYears($number_of_years),
                    'number_of_years' => $number_of_years,
                    'auto_renew' => $request->auto_renew,
                ];

                /**
                 * Update Or Create Model 
                 */
                if (empty($subscriptionModel)) {
                    $subscriptionModel = new Subscription($data);
                    $subscriptionModel->save();
                } else {
                    $subscriptionModel->update($data);
                }

                /**
                 * Update the model in case of trial 
                 */
                if ($isFreeTrial) {

                    $trialDays = DefaultValues::SUBSCRIPTION_TRIAL_PERIOD_DAYS->get();

                    $subscriptionModel->update([
                        'payment_status' => Subscriptions::SUBSCRIPTION_PAYMENT_STATUS_PAID->value,
                        'subscription_status' => Subscriptions::SUBSCRIPTION_STATUS_ACTIVE->value,
                        'auto_renew' => false,
                        'last_payment_date' => now(),
                        'ends_at' => now()->addDays($trialDays),
                        'trial_ends_at' => now()->addDays($trialDays),
                    ]);
                }

                /**
                 * Cleanup 
                 */
                if (!empty($subscriptionModel->id)) {
                    DB::statement('SET FOREIGN_KEY_CHECKS=0');

                    SubscriptionUsage::where('subscription_id', $subscriptionModel->id)->forceDelete();
                    SubscriptionFeature::where('subscription_id', $subscriptionModel->id)->forceDelete();

                    DB::statement('SET FOREIGN_KEY_CHECKS=1');
                }

                /**
                 * Addons Check
                 */
                if (!empty($addons)) {
                    collect($addons)->each(function ($plan_feature_id) use ($subscriptionModel) {
                        $planFeatureModel = PlanFeature::find($plan_feature_id);

                        SubscriptionUsage::create([
                            'subscription_id' => $subscriptionModel->id,
                            'feature_id' => $planFeatureModel->feature_id,
                            'plan_feature_id' => $plan_feature_id,
                            'used' => false
                        ]);

                        SubscriptionFeature::create([
                            'subscription_id' => $subscriptionModel->id,
                            'feature_id' => $planFeatureModel->feature_id,
                            'plan_feature_id' => $plan_feature_id,
                            'value' => true
                        ]);

                        if ($planFeatureModel->isLocBundle()) {
                            $subscriptionModel->update([
                                'assigned_quota' => $planFeatureModel->valid_to_maximum,
                                'remaining_quota' => $planFeatureModel->valid_to_maximum
                            ]);
                        }
                    });
                }

                /**
                 * Design Purposes ( SAAS - On Prem ) 
                 */
                if (!empty($request->feature_id)) {
                    SubscriptionFeature::create([
                        'subscription_id' => $subscriptionModel->id,
                        'feature_id' => $basePlanFeatureModel->feature_id,
                        'plan_feature_id' => $basePlanFeatureModel->plan_feature_id,
                        'value' => true
                    ]);
                }

                /**
                 * Create Subscription History 
                 */
                $subscriptionModel->createHistory(Subscriptions::HISTORY_EVENT_TYPE_NEW);
            }

            /**
             * Step provided_address 
             * Create Customer Billing Address
             */
            if (AppHelper::matchStrings($step, Subscriptions::STEP_PROVIDED_ADDRESS)) {

                if (!empty($request->billing_information)) {
                    $billing = $request->billing_information;

                    CompanyAddress::updateOrCreate(
                        [
                            'company_id' => $customer->company_id,
                            'address_type' => BillingsHelper::ADDRESS_TYPE_BILLING,
                        ],
                        [
                            'country_id' => $request->billing_country_id,
                            'platform_bank_id' => $request->platform_bank_id,
                            'state' => $billing['city'],
                            'city' => $billing['city'],
                            'zip' => $billing['zip'],
                            'address_1' => $billing['detailed_address'],
                            'tax_id' => $billing['tax_id'],
                            'currency_code' => $billing['currency'],
                            'commercial_registration_number' => $billing['company_registration_number'],
                        ]
                    );

                    /**
                     * Update current susbcription currency Id 
                     */
                    if (isset($billing['currency'])) {
                        $currencyId = optional(Currency::where('iso', $billing['currency'])->first())->id;

                        if (!AppHelper::matchStrings($currencyId, $subscriptionModel->currency_id)) {
                            $subscriptionModel->update([
                                'currency_id' => $currencyId
                            ]);
                        }
                    }
                }
            }

            /**
             * Step entered_payment_details 
             * Create Subscription Payment
             */
            if (AppHelper::matchStrings($step, Subscriptions::STEP_ENTERED_PAYMENT_DETAILS)) {

                if (!empty($request->payment_type)) {

                    /**
                     * Wire Transfer Payment 
                     */
                    if ($isWireTransferPayment) {
                        $subscriptionModel->update([
                            'payment_status' => Subscriptions::SUBSCRIPTION_PAYMENT_STATUS_PENDING->value,
                        ]);
                    }

                    SubscriptionPayment::updateOrCreate(
                        [
                            'subscription_id' => $subscriptionModel->id,
                        ],
                        [
                            'payment_type' => $request->payment_type,
                            'payment_gateway' => $request->payment_gateway,
                            'payment_status' => Subscriptions::SUBSCRIPTION_PAYMENT_STATUS_PENDING->value,
                        ]
                    );
                }
            }

            /**
             * Step confirmed_order
             * Upload Receipt if any
             */
            if (AppHelper::matchStrings($step, Subscriptions::STEP_CONFIRMED_ORDER)) {

                // Fill the platform bank id
                $bankAddressModel = CompanyAddress::where([
                    ['company_id', $customer->company_id],
                    ['address_type', BillingsHelper::ADDRESS_TYPE_BILLING]
                ])->first();

                if (!empty($bankAddressModel)) {
                    $bankAddressModel->update([
                        'platform_bank_id' => $request->platform_bank_id,
                    ]);
                }

                /**
                 * Create & Upload Receipt File to GCS
                 * Non Trial => check if receipt uploaded or not 
                 */
                if ($isWireTransferPayment && $request->hasFile('receipt_file')) {

                    $files = $request->file('receipt_file');

                    SubscriptionHelper::uploadReceipts($subscriptionModel, $files);
                }
                /**
                 * Upload Later 
                 */
                elseif (!$isFreeTrial) {
                    $subscriptionModel->update([
                        'subscription_status' => Subscriptions::SUBSCRIPTION_STATUS_PENDING_RECEIPT->value,
                        'payment_status' => Subscriptions::SUBSCRIPTION_PAYMENT_STATUS_PENDING->value,
                    ]);
                }

                $subscriptionModel->createHistory(Subscriptions::HISTORY_EVENT_TYPE_PAYMENT_UPDATE);
            }

            /**
             * In Case Of Saas => Set the ends_at to null for now 
             */
            if ($subscriptionModel->isSaas()) {
                $subscriptionModel->update([
                    "starts_at" => null,
                    "ends_at" => null
                ]);
            }

            $subscriptionModel->fresh();

            /**
             * Log Step 
             */
            $this->subscriptionProgressService->withSubscription($subscriptionModel, $step)->stepForward();

            /**
             * Activate Observer
             */
            SubscriptionObserver::$skipObserver = false;

            DB::commit();

            return $this->apiResponse->props(ApiStatus::SUCCESS)
                ->withData((new SubscriptionResource($subscriptionModel))->toArray($request))
                ->send();
        } catch (\Exception $e) {
            DB::rollBack();

            $errorMessage = mb_strimwidth($e->getMessage(), 0, 250, '...');

            $this->logService
                ->withRequest($request)
                ->props(Subscriptions::SUBSCRIPTION_LOG_EVENT_TYPE_NEW->value, Subscriptions::SUBSCRIPTION_LOG_EVENT_TYPE_NEW->description(), $errorMessage)
                ->withFailure()
                ->log();

            return $this->apiResponse->props(ApiStatus::BAD_REQUEST, SubscriptionMessages::SUBSCRIPTION_FAILURE->description())
                ->withSystemMessages('Error', $e->getMessage())
                ->send();
        }
    }

    public function estimatedPrice(Request $request)
    {
        $user = CustomerHelper::getCompanyUsers($this->apiResponse, self::getBlamableId());

        if ($user instanceof \Illuminate\Http\JsonResponse) {
            return $user;
        }

        $validator = Validator::make($request->all(), [
            'subscription_id' => 'integer|exists:subscriptions,id',
            'plan_id' => 'integer|required|exists:plans,id',
            'billing_country_id' => 'integer|required|exists:countries,id',
            'platform_country_id' => 'integer|exists:platform_countries,platform_country_id',
            'addons' => 'array',
            'addons.*' => 'integer|exists:plan_features,plan_feature_id',
            'currency_id' => 'integer|exists:currencies,id',
            'number_of_years' => 'sometimes|integer|min:1',
        ]);

        if ($validator->fails()) {
            return ApiHelper::validationError($this->apiResponse, $validator->errors()->toArray());
        }

        $priceData = BillingsHelper::calculateEstimatedPrice(
            planId: $request->input('plan_id'),
            billingCountryId: $request->input('billing_country_id'),
            newCurrencyId: $request->input('currency_id'),
            addons: $request->input('addons', []),
            platformCountryId: $request->input('platform_country_id'),
            numberOfYears: $request->input('number_of_years'),
            subscriptionId: $request->input('subscription_id')
        );

        if (empty($priceData)) {
            return $this->apiResponse->props(ApiStatus::BAD_REQUEST)
                ->withSystemMessages('Plan not found.')
                ->send(404);
        }

        $out = [
            "total_before_discount" => $this->moneyFormatter->price($priceData['price']->getAmount(), $priceData['price']->getCurrency()->getCode())->format(),
            "discount_amount" => $this->moneyFormatter->price($priceData['discount']->getAmount(), $priceData['discount']->getCurrency()->getCode())->format(),
            "total_after_discount" => $this->moneyFormatter->price($priceData['total_discounted']->getAmount(), $priceData['total_discounted']->getCurrency()->getCode())->format(),
            "vat_percentage" => (int)($priceData['vat_percentage'] * 100) . '%',
            "vat_amount" => $this->moneyFormatter->price($priceData['vat_amount']->getAmount(), $priceData['vat_amount']->getCurrency()->getCode())->format(),
            "grand_total" => $this->moneyFormatter->price($priceData['grand_total']->getAmount(), $priceData['grand_total']->getCurrency()->getCode())->format(),
        ];

        return $this->apiResponse->props(ApiStatus::SUCCESS)
            ->withData($out)
            ->send();
    }

    /**
     * Get address details by billing country id
     */
    public function getAddressDetailsByBillingCountryId(int $id, Request $request)
    {

        $user = CustomerHelper::getCompanyUsers($this->apiResponse, self::getBlamableId());

        if ($user instanceof \Illuminate\Http\JsonResponse) {
            return $user;
        }

        $company = $user->company;

        $address = $company->billingAddress(countryId: $id) ?? new CompanyAddress([]);

        return $this->apiResponse->props(ApiStatus::SUCCESS)
            ->withData((new CompanyAddressResource($address))->toArray($request))
            ->send();
    }

    public function getReceiptsFiles(int $subscriptionId, Request $request)
    {

        $user = CustomerHelper::getCompanyUsers($this->apiResponse, self::getBlamableId());

        if ($user instanceof \Illuminate\Http\JsonResponse) {
            return $user;
        }

        $subscription = Subscription::find($subscriptionId);

        if (empty($subscription)) {
            return $this->apiResponse->props(ApiStatus::NOT_FOUND)
                ->withSystemMessages(ValidationMessages::SUBSCRIPTION_NOT_FOUND)
                ->send();
        }

        $receipts = $subscription->receiptsFiles();

        return $this->apiResponse->props(ApiStatus::SUCCESS)
            ->withData($receipts)
            ->send();
    }

    /**
     * Upload Receipt  
     */
    public function uploadReceipts(int $subscriptionId, Request $request)
    {
        SubscriptionObserver::$skipObserver = true;

        $user = CustomerHelper::getCompanyUsers($this->apiResponse, self::getBlamableId());

        if ($user instanceof \Illuminate\Http\JsonResponse) {
            return $user;
        }

        $validator = Validator::make($request->all(), [
            'receipt_file.*' => 'file|mimes:jpeg,png,jpg,pdf|max:5120',
            'receipt_file' => 'required|array|min:1',
        ], [
            'receipt_file.*.mimes' => SubscriptionMessages::SUBSCRIPTION_INVALID_RECEIPT->description(),
        ]);

        if ($validator->fails()) {
            return ApiHelper::validationError($this->apiResponse, $validator->errors()->toArray());
        }

        $subscriptionModel = Subscription::find($subscriptionId);

        /**
         * IS User Allowed To Modify Subscription 
         */
        if (empty($subscriptionModel) || !AppHelper::matchStrings($user->company_id, $subscriptionModel->subscriber_id)) {
            return $this->apiResponse->props(ApiStatus::NOT_FOUND)
                ->withSystemMessages(ValidationMessages::SUBSCRIPTION_NOT_FOUND)
                ->send();
        }

        // upload logic
        if ($request->hasFile('receipt_file')) {
            $files = $request->file('receipt_file');

            if (SubscriptionHelper::uploadReceipts($subscriptionModel, $files)) {

                /**
                 * Create History Record
                 */
                $subscriptionModel->createHistory(Subscriptions::HISTORY_EVENT_TYPE_PAYMENT_UPDATE);

                $subscriptionModel = $subscriptionModel->fresh();

                return $this->apiResponse->props(ApiStatus::SUCCESS)
                    ->withData((new SubscriptionResource($subscriptionModel))->toArray($request))
                    ->send();
            }
        }

        SubscriptionObserver::$skipObserver = false;

        return $this->apiResponse->props(ApiStatus::BAD_REQUEST)
            ->withSystemMessages(ValidationMessages::SUBSCRIPTION_RECEIPT_UPLOAD_FAILURE)
            ->send();
    }

    /**
     * Upload Server Ids 
     */
    public function uploadServerId(int $subscriptionId, Request $request)
    {

        $user = CustomerHelper::getCompanyUsers($this->apiResponse, self::getBlamableId());

        if ($user instanceof \Illuminate\Http\JsonResponse) {
            return $user;
        }

        $validator = Validator::make($request->all(), [
            'server_id' => ['string', 'required', 'unique:subscription_licenses,server_id', new \App\Rules\SonarServerId],
            'server_type' => 'string|required', // development, test (uat), production
        ]);

        if ($validator->fails()) {
            return ApiHelper::validationError($this->apiResponse, $validator->errors()->toArray());
        }

        $subscriptionModel = Subscription::find($subscriptionId);
        $isSaas = $subscriptionModel?->isSaas();

        /**
         * IS User Allowed To Modify Subscription 
         */
        if (empty($subscriptionModel) || !AppHelper::matchStrings($user->company_id, $subscriptionModel->subscriber_id)) {
            return $this->apiResponse->props(ApiStatus::NOT_FOUND)
                ->withSystemMessages(ValidationMessages::SUBSCRIPTION_NOT_FOUND)
                ->send();
        }

        $isFreeTrial = $subscriptionModel->plan?->isFreeTrial();
        $licenseType = $isFreeTrial ? Subscriptions::LICENSE_TYPE_EVALUATION->value : Subscriptions::LICENSE_TYPE_COMMERCIAL->value;

        /**
         * Create or update Subscription Licenses 
         */
        $license = SubscriptionLicense::updateOrCreate(
            [
                'subscription_id' => $subscriptionId,
                'company_id' => $user->company_id,
                'environment' => $request->server_type,
            ],
            [
                'server_id' => $request->server_id,
                'request_date' => now(),
                'license_type' => $licenseType,
                'sonar_username' => config('settings.sonarqube.default_username'),
                'sonar_password' => Crypt::encryptString(config('settings.sonarqube.default_password')),
            ]
        );

        $subscriptionService = app(SubscriptionService::class);

        /**
         * Send Email 
         */
        if ($subscriptionModel->subscriber && $subscriptionModel->subscriber->primaryContact) {
            Mail::to($subscriptionModel->subscriber->primaryContact->email)
                ->queue(new LicenseRequestAcknowledgmentMail($subscriptionModel, $license));

            // Create notification for license request
            $notificationService = app(NotificationService::class);
            $notificationService->createNotification(
                $subscriptionModel->subscriber->primaryContact,
                Notifications::NOTIFICATION_EVENT_LICENSE_REQUESTED->value,
                [
                    'user_name' => $subscriptionModel->subscriber->primaryContact->name,
                    'subscription_id' => $subscriptionModel->public_subscription_id
                ]
            );
        }

        return $this->apiResponse->props(ApiStatus::SUCCESS)
            ->withData((new SubscriptionResource($subscriptionModel))->toArray($request))
            ->send();
    }

    /**
     * Complete Env Deployment 
     */
    public function deploymentIsComplete(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'domain' => 'string|required', // without https://
            'subscription_id' => 'int|required|exists:subscriptions,id',
        ]);

        if ($validator->fails()) {
            return ApiHelper::validationError($this->apiResponse, $validator->errors()->toArray());
        }

        $subscriptionModel = Subscription::find($request->subscription_id);

        SubscriptionLicenseObserver::$skipObserver = true;

        $sonarUrl = "https://" . rtrim($request->domain, '/');

        /**
         * Update subscription process 
         */
        $proccessModel = SubscriptionLicense::where('sonar_url', $sonarUrl)
            ->first();

        if (!empty($proccessModel)) {

            $proccessModel->update([
                'environment_status' => SubscriptionLicenses::ENVIRONMENT_STATUS_ACTIVE->value,
            ]);

            $proccessModel->fresh();

            /**
             * let's use the sonarQube service to establish a connection to the created instance & fetch it's server ID  
             */
            $decryptedToken = null;

            if (empty($proccessModel->sonar_api_token)) {
                $decryptedToken = app(SonarQubeTokenService::class)->generateAndStoreToken(
                    $proccessModel,
                    'client_' . $subscriptionModel->subscriber_id . '_license_' . $proccessModel->subscription_license_id
                );

                if (!$decryptedToken) {
                    if ($proccessModel->isDirty()) {
                        $proccessModel->save();
                    }
                    return $this->apiResponse->props(ApiStatus::BAD_REQUEST, "Failed to generate SonarQube API token. Instance details partially updated.")
                        ->withData(['subscription_id' => $subscriptionModel->id])
                        ->send();
                }
            } else {
                $decryptedToken = AppHelper::decryptString($proccessModel->sonar_api_token);
            }

            $proccessModel->fresh();

            $sonarClient = new SonarQubeService($sonarUrl, $proccessModel->sonar_api_token);
            $serverId = $sonarClient->getServerId();

            if ($serverId) {
                $proccessModel->server_id = $serverId;
                $proccessModel->save();
            }

            return $this->apiResponse->props(ApiStatus::SUCCESS, "Deployment completed and SonarQube integrated.")
                ->withData([
                    'subscription_id' => $subscriptionModel->id,
                    'sonar_url' => $proccessModel->sonar_url,
                    'token_generated' => !empty($proccessModel->sonar_api_token),
                    'server_id_fetched' => !empty($proccessModel->server_id)
                ])
                ->send();
        }

        SubscriptionLicenseObserver::$skipObserver = false;

        return $this->apiResponse->props(ApiStatus::BAD_REQUEST)
            ->withSystemMessages("Proccess Model Was Not Found!")
            ->send();
    }
}
