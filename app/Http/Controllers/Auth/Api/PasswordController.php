<?php

namespace App\Http\Controllers\Auth\Api;

use App\Enums\ApiSettings;
use App\Enums\ApiStatus;
use App\Enums\AuthEvents;
use App\Enums\Notifications;
use App\Enums\ValidationMessages;
use App\Helpers\ApiHelper;
use App\Helpers\AppHelper;
use App\Helpers\StatusHelper;
use App\Http\Controllers\Controller;
use App\Mail\OtpVerificationMail;
use App\Mail\WelcomeMail;
use App\Models\User;
use App\Services\ApiResponseService;
use App\Services\LogService;
use App\Services\NotificationService;
use App\Services\OtpService;
use App\Traits\Blamable;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Tymon\JWTAuth\Facades\JWTAuth;

class PasswordController extends Controller
{
    use Blamable;

    protected $apiResponse;

    protected $logService;

    protected $notificationService;

    protected $otpService;

    public function __construct(ApiResponseService $apiResponse, LogService $logService, NotificationService $notificationService, OtpService $otpService)
    {
        $this->apiResponse = $apiResponse;
        $this->logService = $logService;
        $this->notificationService = $notificationService;
        $this->otpService = $otpService;
    }


    public function set(Request $request)
    {
        \OwenIt\Auditing\Auditable::disableAuditing();

        $validator = Validator::make($request->all(), [
            'token' => 'string',
            'email' => 'string|required|email|max:255',
            'password' => [
                'required',
                'string',
                'min:8',
                'confirmed',
                'regex:/[a-z]/', // At least one lowercase letter
                'regex:/[A-Z]/', // At least one uppercase letter
                'regex:/[0-9]/', // At least one number
                'regex:/[@$!%*?&]/', // At least one symbol (e.g. !@#$%)
            ],
        ], [
            'password.regex' => ValidationMessages::WEAK_PASSWORD->description(),
        ]);

        if ($validator->fails()) {
            $this->logService
                ->withRequest($request)
                ->props(AuthEvents::PASSWORD_SET, AuthEvents::PASSWORD_SET->description(),  ValidationMessages::VALIDATION_ERROR->description(), $validator->errors()->toArray())
                ->withFailure()
                ->log();

            return ApiHelper::validationError($this->apiResponse, $validator->errors()->toArray());
        }

        $requestHasToken = $request->filled('token');

        $user = User::query()
            ->when($request->filled('email'), fn($q) => $q->where('email', $request->email))
            ->when($requestHasToken, fn($q) => $q->where('invitation_token', $request->token))
            ->first();

        if (empty($user)) {
            $this->logService
                ->withRequest($request)
                ->props(AuthEvents::PASSWORD_SET, AuthEvents::PASSWORD_SET->description(), ValidationMessages::USER_NOT_FOUND->description())
                ->withFailure()
                ->log();

            return $this->apiResponse->props(ApiStatus::BAD_REQUEST, ValidationMessages::USER_NOT_FOUND->description())
                ->send();
        }

        /**
         * Validate invitation token 
         */
        if (!empty($request->token)) {
            if (
                empty($user->invitation_token) || (!AppHelper::matchStrings($user->invitation_token, $request->token)) ||
                empty($user->invitation_expires_at) || $user->invitation_expires_at->isPast()
            ) {
                $this->logService
                    ->withRequest($request)
                    ->props(AuthEvents::PASSWORD_SET, AuthEvents::PASSWORD_SET->description(), ValidationMessages::INVALID_OR_EXPIRED_TOKEN->description())
                    ->withFailure()
                    ->log();

                return $this->apiResponse->props(ApiStatus::BAD_REQUEST, ValidationMessages::INVALID_OR_EXPIRED_TOKEN->description())
                    ->send();
            }
        }

        /**
         * User Accepts Invitation 
         * Primary User Login Through Invitation Link => both user & company should be active
         */
        if ($requestHasToken) {
            $user->accepted = true;
            $user->status_id = StatusHelper::getStatusByAttributeName('name', 'active', StatusHelper::STATUS_TYPE_USER);

            $user->save();

            if ($user->isPrimary()) {
                $user->company->status_id = StatusHelper::getStatusByAttributeName('name', 'active');
                $user->company->save();
            }
        }

        $user->password = Hash::make($request->password);
        $user->invitation_token = null;
        $user->invitation_expires_at = null;
        $user->email_verified_at = now();

        /**
         * Unlock the user after a successful password change
         */
        $user->locked = false;
        $user->login_attempts = 0;
        $user->locked_until = null;

        $user->saveQuietly();

        $token = JWTAuth::fromUser($user);

        Auth::login($user);

        Mail::to($user->email)->queue(new WelcomeMail($user));

        // Create notification for account created/welcome
        $this->notificationService->createNotification(
            $user,
            Notifications::NOTIFICATION_EVENT_ACCOUNT_CREATED->value,
            [
                'user_name' => $user->name,
            ]
        );

        $this->logService
            ->withRequest($request)
            ->props(AuthEvents::PASSWORD_SET, AuthEvents::PASSWORD_SET->description())
            ->log();

        \OwenIt\Auditing\Auditable::enableAuditing();

        return $this->apiResponse->props(ApiStatus::CREATED)
            ->withData(["token" => $token])
            ->send();
    }

    public function forgot(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|string|email|max:255',
        ]);

        if ($validator->fails()) {
            $this->logService
                ->withRequest($request)
                ->props(AuthEvents::PASSWORD_FORGOT, AuthEvents::PASSWORD_FORGOT->description(),  ValidationMessages::VALIDATION_ERROR->description(), $validator->errors()->toArray())
                ->withFailure()
                ->log();

            return ApiHelper::validationError($this->apiResponse, $validator->errors()->toArray());
        }

        $user = User::where('email', $request->email)->first();

        if (empty($user)) {
            $this->logService
                ->withRequest($request)
                ->props(AuthEvents::PASSWORD_FORGOT, AuthEvents::PASSWORD_FORGOT->description(), ValidationMessages::USER_NOT_FOUND->description())
                ->withFailure()
                ->log();

            return $this->apiResponse->props(ApiStatus::BAD_REQUEST, ValidationMessages::USER_NOT_FOUND->description())
                ->send();
        }

        $otp = $this->otpService->withUser($user, ApiSettings::OTP_EXPIRY_MINUTES->getValue())->generate(ApiSettings::OTP_DEFAULT_RANGE->getValue());

        Mail::to($user->email)->queue(new OtpVerificationMail($otp, $user, 'Password Reset'));

        $this->logService
            ->withRequest($request)
            ->props(AuthEvents::PASSWORD_FORGOT, AuthEvents::PASSWORD_FORGOT->description())
            ->log();

        return $this->apiResponse->props(ApiStatus::SUCCESS)
            ->send();
    }

    public function reset(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|string|email|max:255',
            'password' => 'required|string|min:8|confirmed',
        ]);

        if ($validator->fails()) {
            $this->logService
                ->withRequest($request)
                ->props(AuthEvents::PASSWORD_RESET, AuthEvents::PASSWORD_RESET->description(),  ValidationMessages::VALIDATION_ERROR->description(), $validator->errors()->toArray())
                ->withFailure()
                ->log();

            return ApiHelper::validationError($this->apiResponse, $validator->errors()->toArray());
        }

        $user = User::where('email', $request->email)->first();

        if (empty($user)) {
            $this->logService
                ->withRequest($request)
                ->props(AuthEvents::PASSWORD_RESET, AuthEvents::PASSWORD_RESET->description(), ValidationMessages::USER_NOT_FOUND->description())
                ->withFailure()
                ->log();

            return $this->apiResponse->props(ApiStatus::BAD_REQUEST, ValidationMessages::USER_NOT_FOUND->description())
                ->send();
        }

        /**
         * Unlock the user after a successful password change
         */
        $user->locked = false;
        $user->login_attempts = 0;
        $user->locked_until = null;

        $user->password = Hash::make($request->password);
        $user->saveQuietly();

        $token = JWTAuth::fromUser($user);

        Auth::login($user);

        // Create notification for password reset
        $this->notificationService->createNotification(
            $user,
            Notifications::NOTIFICATION_EVENT_ACCOUNT_PASSWORD_RESET->value,
            [
                'user_name' => $user->name,
            ]
        );

        $this->logService
            ->withRequest($request)
            ->props(AuthEvents::PASSWORD_RESET, AuthEvents::PASSWORD_RESET->description())
            ->log();

        return $this->apiResponse->props(ApiStatus::SUCCESS)
            ->withData(["token" => $token])
            ->send();
    }

    public function change(Request $request)
    {
        $logService = $this->logService->withRequest($request);

        $validator = Validator::make($request->all(), [
            'otp' => 'required|string',
            'password' => [
                'required',
                'string',
                'min:8',
                'confirmed',
                'regex:/[a-z]/', // At least one lowercase letter
                'regex:/[A-Z]/', // At least one uppercase letter
                'regex:/[0-9]/', // At least one number
                'regex:/[@$!%*?&]/', // At least one symbol (e.g. !@#$%)
            ],
        ], [
            'password.regex' => ValidationMessages::WEAK_PASSWORD->description(),
        ]);

        if ($validator->fails()) {
            $logService
                ->withRequest($request)
                ->props(AuthEvents::PASSWORD_CHANGE, AuthEvents::PASSWORD_CHANGE->description(),  ValidationMessages::VALIDATION_ERROR->description(), $validator->errors()->toArray())
                ->withFailure()
                ->log();

            return ApiHelper::validationError($this->apiResponse, $validator->errors()->toArray());
        }

        $user = self::getBlamable();

        if (!empty($user)) {
            $otpService = $this->otpService->withUser($user);

            // OTP expired
            if ($user->otp_expires_at->isPast()) {
                $logService
                    ->props(AuthEvents::PASSWORD_CHANGE, AuthEvents::PASSWORD_CHANGE->description(),  ValidationMessages::EXPIRED_OTP->description())
                    ->withFailure()
                    ->log();

                return $this->apiResponse->props(ApiStatus::BAD_REQUEST, ValidationMessages::EXPIRED_OTP->description())
                    ->send();
            }

            if (!$otpService->isValid($request->otp)) {

                // invalid
                if (!empty($user->otp_attempts) && $user->otp_attempts >= ApiSettings::OTP_LOCKING_FAILED_ATTEMPTS->getValue()) {
                    $otpService->lock(ApiSettings::OTP_LOCKING_MINUTES->getValue());

                    $logService
                        ->props(AuthEvents::OTP_VALIDATE, AuthEvents::OTP_VALIDATE->description(),  ValidationMessages::OTP_LOCKED->description())
                        ->withFailure()
                        ->log();

                    return $this->apiResponse->props(ApiStatus::BAD_REQUEST, ValidationMessages::INVALID_OTP->description())
                        ->withSystemMessages(ValidationMessages::OTP_LOCKED)
                        ->send();
                }

                $logService
                    ->props(AuthEvents::OTP_VALIDATE, AuthEvents::OTP_VALIDATE->description(),  ValidationMessages::INVALID_OTP->description())
                    ->withFailure()
                    ->log();

                return $this->apiResponse->props(ApiStatus::BAD_REQUEST, ValidationMessages::INVALID_OTP->description())
                    ->send();
            } else {
                $user->password = Hash::make($request->password);

                /**
                 * Unlock the user after a successful password change
                 */
                $user->locked = false;
                $user->login_attempts = 0;
                $user->locked_until = null;
                $user->saveQuietly();

                JWTAuth::invalidate(JWTAuth::getToken());

                $token = JWTAuth::fromUser($user);

                $otpService->reset();

                // Create notification for password reset
                $this->notificationService->createNotification(
                    $user,
                    Notifications::NOTIFICATION_EVENT_ACCOUNT_PASSWORD_RESET->value,
                    [
                        'user_name' => $user->name,
                    ]
                );

                $this->logService
                    ->withRequest($request)
                    ->props(AuthEvents::PASSWORD_SET, AuthEvents::PASSWORD_SET->description())
                    ->log();

                return $this->apiResponse->props(ApiStatus::SUCCESS)
                    ->withData(["token" => $token])
                    ->send();
            }
        }

        $this->logService
            ->withRequest($request)
            ->props(AuthEvents::PASSWORD_CHANGE, AuthEvents::PASSWORD_CHANGE->description(), ValidationMessages::INVALID_OTP->description())
            ->withFailure()
            ->log();

        return $this->apiResponse->props(ApiStatus::BAD_REQUEST, ValidationMessages::INVALID_OTP->description())
            ->send();
    }
}
