<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;

use App\Helpers\AppHelper;
use App\Helpers\StatusHelper;
use App\Scopes\SortingScope;
use App\Traits\Blamable;
use App\Traits\Timezones;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Str;
use Laravel\Sanctum\HasApiTokens;
use Laravelcm\Subscriptions\Traits\HasPlanSubscriptions;
use OwenIt\Auditing\Contracts\Auditable;
use App\Traits\HasRoles;
use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Tymon\JWTAuth\Contracts\JWTSubject;

class User extends Authenticatable implements Auditable, JWTSubject, FilamentUser
{
    use \OwenIt\Auditing\Auditable;
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use SoftDeletes, HasFactory, Notifiable, HasRoles, HasPlanSubscriptions, HasApiTokens, Timezones, Blamable;

    /**
     * The primary key column name.
     *
     * @var string
     */
    protected $primaryKey = 'user_id';

    public function getAuthIdentifierName()
    {
        return 'user_id';
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'public_user_id',
        'google_id',
        'azure_id',
        'company_id',
        'status_id',
        'name',
        'email',
        'password',
        'otp',
        'otp_expires_at',
        'timezone',
        'email_verified_at',
        'phone_number',
        'status',
        'invitation_token',
        'invitation_expires_at',
        'accepted',
        'description',
        'type',
        'locked',
        'login_attempts',
        'locked_until',
        'otp_attempts',
        'otp_locked_until',
        'created_at',
        'updated_at',
        'deleted_at',
        'created_by',
        'updated_by',
        'deleted_by',
        'last_activity_at',
        'is_primary_customer',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'otp_expires_at' => 'datetime',
            'locked_until' => 'datetime',
            'otp_locked_until' => 'datetime',
            'last_activity_at' => 'datetime',
            'invitation_expires_at' => 'datetime',
            'password' => 'hashed',
            'countries' => 'array',
        ];
    }


    /**
     * Get the identifier that will be stored in the subject claim of the JWT.
     *
     * @return mixed
     */
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    /**
     * Return a key-value array, containing any custom claims to be added to the JWT.
     *
     * @return array
     */
    public function getJWTCustomClaims()
    {
        return [];
    }

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id');
    }

    public function status()
    {
        return $this->belongsTo(Status::class, 'status_id', 'status_id');
    }

    public function logs()
    {
        return $this->hasMany(Log::class, 'user_id')->orWhere('email', $this->email);
    }

    // public function findCustomer($query): \Illuminate\Database\Eloquent\Builder
    // {
    //     return $query->where('type', 'customer');
    // }

    /**
     * Attributes to exclude from the Audit.
     *
     * @var array
     */
    protected $auditExclude = [
        'login_attempts',
        'otp_attempts',
        'locked_until',
        'otp_locked_until',
        'remember_token',
        'password'
    ];

    /**
     * Only audit attributes that have actually changed.
     *
     * @var bool
     */
    //    protected $auditTimestamps = false;

    /**
     * Modify the audit before storing it.
     *
     * @param array $data
     * @return array
     */
    public function transformAudit(array $data): array
    {
        // For updates, only include attributes that actually changed
        if ($data['event'] === 'updated') {
            $oldValues = $data['old_values'] ?? [];
            $newValues = $data['new_values'] ?? [];

            // Special handling for locked and is_primary_customer
            // Only include them if they were explicitly changed
            foreach (['locked', 'is_primary_customer'] as $field) {
                if (isset($newValues[$field])) {
                    // Convert boolean values to integers (0 or 1) for consistent comparison
                    if (isset($oldValues[$field])) {
                        $oldValues[$field] = (int)$oldValues[$field];
                    } else {
                        $oldValues[$field] = 0; // Default to 0 if not set
                    }
                    $newValues[$field] = (int)$newValues[$field];

                    // If values are the same after normalization, remove them
                    if ($oldValues[$field] === $newValues[$field]) {
                        unset($data['old_values'][$field]);
                        unset($data['new_values'][$field]);
                    } else {
                        // Update the values in the data array to use consistent integers
                        $data['old_values'][$field] = $oldValues[$field];
                        $data['new_values'][$field] = $newValues[$field];
                    }
                }
            }

            // Filter out other values that didn't actually change
            foreach ($newValues as $attribute => $value) {
                if (
                    !in_array($attribute, ['locked', 'is_primary_customer']) &&
                    isset($oldValues[$attribute]) && $oldValues[$attribute] === $value
                ) {
                    unset($data['old_values'][$attribute]);
                    unset($data['new_values'][$attribute]);
                }
            }

            // If no changes remain, don't create an audit
            if (empty($data['new_values'])) {
                $data['old_values'] = [];
                $data['new_values'] = [];
            }
        }

        return $data;
    }

    /**
     * Determine if the company user profile is complete.
     */
    public function isProfileCompleted(): bool
    {
        return !empty($this->email) && !empty($this->phone_number) && !empty($this->name);
    }

    protected static function booted()
    {
        static::addGlobalScope(new SortingScope());

        static::creating(function ($model) {
            if (app()->runningInConsole()) {
                $model->type = "admin";
            }

            self::setCreatedUpdatedBy($model);
        });

        static::created(function ($model) {
            $model->public_user_id = AppHelper::getFormattedId($model->user_id, 'CUSTU_');
            $model->saveQuietly();
        });

        static::saving(function ($model) {
            self::setUpdatedBy($model);
        });

        static::deleting(function ($model) {
            self::setDeletedBy($model);
        });

        static::restored(function ($model) {});

        return parent::booted();
    }

    /**
     * Check if the user is a primary contact.
     */
    public function isPrimary(): bool
    {
        return $this->is_primary_customer;
    }

    public function canAccessPanel(Panel $panel): bool
    {
        if (config('app.env') === 'local') {
            return true;
        }

        $allowedDomains = [
            '@ismena.com',
            '@isolution.sa',
            '@isolutions.sa',
            '@sonarsource.com',
        ];

        foreach ($allowedDomains as $domain) {
            if (str_ends_with($this->email, $domain)) {
                return true;
            }
        }

        return false;
    }

    public function invitationLink()
    {
        return AppHelper::getConfigValue('next-js.base_url') . "/set-password?token={$this->invitation_token}&email={$this->email}";
    }

    public function notifications()
    {
        return $this->hasMany(Notification::class, 'user_id', 'user_id');
    }

    public function notificationSettings()
    {
        return $this->hasMany(UserNotificationSetting::class, 'user_id', 'user_id')
            ->where('is_visible', true);
    }
}
