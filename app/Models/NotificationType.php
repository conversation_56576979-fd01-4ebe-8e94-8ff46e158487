<?php

namespace App\Models;

use App\Scopes\SortingScope;
use App\Traits\Blamable;
use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;

class NotificationType extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable, Blamable;

    /**
     * The primary key column name.
     *
     * @var string
     */
    protected $primaryKey = 'notification_type_id';

    /**
     * The table associated with the model.
     *
     * @var string|null
     */
    protected $table = 'notification_types';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'default_channels',
        'event',
        'placeholders',
        'is_visible',
        'created_at',
        'updated_at',
        'created_by',
        'updated_by',
        'deleted_at',
        'deleted_by'
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'default_channels' => 'array',
            'placeholders' => 'array',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
            //'password' => 'hashed',
            //'countries' => 'array',
        ];
    }

    /**
     * Attributes to include in the Audit.
     *
     * @var array
     */
    // protected $auditInclude = [
    //     'title',
    //     'content',
    // ];

    public function translations()
    {
        return $this->hasMany(NotificationTypeTranslation::class, 'type_id', 'notification_type_id');
    }

    public function translation()
    {
        return $this->hasOne(NotificationTypeTranslation::class, 'type_id', 'notification_type_id')->where('language_code', app()->getLocale());
    }

    protected static function booted()
    {
        static::addGlobalScope(new SortingScope());

        static::creating(function ($model) {
            self::setCreatedUpdatedBy($model);
        });

        static::saving(function ($model) {
            self::setUpdatedBy($model);
        });

        static::deleting(function ($model) {
            self::setDeletedBy($model);
        });

        return parent::booted();
    }
}
