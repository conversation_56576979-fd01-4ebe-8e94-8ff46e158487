<?php

namespace App\Services;

use App\Enums\DefaultValues;
use App\Enums\Notifications;
use App\Models\User;
use App\Models\Notification;
use App\Models\NotificationType;
use App\Models\UserNotificationSetting;
use App\Traits\Timezones;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class NotificationService
{
    use Timezones;

    /**
     * Create a new notification for a user, creating multiple rows if the notification
     * type specifies multiple delivery channels.
     *
     * @param User $user The user receiving the notification.
     * @param string $notificationTypeName The name of the notification type (e.g., 'account_created').
     * @param array $templateParams Parameters to be replaced in the template.
     * @param string|null $linkUrl An optional URL for the notification.
     * @return Notification|null The last created notification instance, or null if the user opted out.
     */
    public function createNotification(User $user, string $notificationTypeName, array $templateParams = [], ?string $linkUrl = null): ?Notification
    {
        $notificationType = NotificationType::where('name', $notificationTypeName)->firstOrFail();

        $userSetting = UserNotificationSetting::where('user_id', $user->user_id)
            ->where('notification_type_id', $notificationType->notification_type_id)
            ->first();

        if ($userSetting && !$userSetting->is_enabled) {
            return null; // User has opted out.
        }

        $channels = $notificationType?->default_channels;

        $baseData = [
            'user_id' => $user->user_id,
            'notification_type_id' => $notificationType->notification_type_id,
            'template_params' => $templateParams,
            'link_url' => $linkUrl,
        ];

        if (is_array($channels) && !empty($channels)) {
            $lastNotification = null;

            foreach ($channels as $channel) {
                $lastNotification = Notification::create(array_merge($baseData, [
                    'delivery_channels' => [$channel],
                ]));
            }

            return $lastNotification;
        } else {
            return Notification::create($baseData);
        }
    }

    /**
     * Retrieve a paginated and consistently nested-grouped list of notifications for a user.
     *
     * @param User $user The user whose notifications are being fetched.
     * @param int $perPage The number of notifications to show per page.
     * @return array A structured array with pagination info and nested-grouped notifications.
     */
    public function getNotificationsForUser(User $user, int $perPage = 15): array
    {
        $paginatedNotifications = $user->notifications()
            ->with(['notificationType.translation'])
            ->where('is_archived', false)
            ->whereJsonContains('delivery_channels', Notifications::NOTIFICATION_CHANNEL_DATABASE->value)
            ->whereHas('notificationType', function ($subQuery) {
                $subQuery->whereNotIn('event', [
                    Notifications::NOTIFICATION_EVENT_ACCOUNT_OTP_SENT->value,
                    Notifications::NOTIFICATION_EVENT_ACCOUNT_INVITATION_SENT->value,
                ]);
            })
            ->latest()
            ->paginate($perPage);

        $defaultDateFormat = DefaultValues::DATE_FORMAT->get();

        $transformedNotifications = $paginatedNotifications->through(function ($notification) use ($defaultDateFormat) {

            $createdAtCarbon = $notification->created_at;

            if ($createdAtCarbon->isToday()) {
                $dateGroup = "Today, " . $createdAtCarbon->format($defaultDateFormat);
            } elseif ($createdAtCarbon->isYesterday()) {
                $dateGroup = "Yesterday, " . $createdAtCarbon->format($defaultDateFormat);
            } else {
                $dateGroup = $createdAtCarbon->format($defaultDateFormat);
            }

            $formattedDateTime = self::formatDateTime($createdAtCarbon);

            $templateString = $notification->getFormattedMessage();

            return [
                'id' => $notification->notification_id,
                'content' => $templateString,
                'link_url' => $notification->link_url,
                'is_read' => $notification->is_read,
                'created_at' => $formattedDateTime,
                'date_group' => $dateGroup,        
                'type' => $notification->notificationType->name,
                'description' => $notification->notificationType->translation?->description,
            ];
        });

        $notificationsOnPage = $transformedNotifications->getCollection();

        $groupByDate = function (Collection $collection) {
            return $collection->groupBy('date_group');
        };

        return [
            'pagination' => [
                'total' => $paginatedNotifications->total(),
                'per_page' => $paginatedNotifications->perPage(),
                'current_page' => $paginatedNotifications->currentPage(),
                'last_page' => $paginatedNotifications->lastPage(),
            ],
            'notifications' => [
                'All' => $groupByDate($notificationsOnPage),
                'Unread' => $groupByDate($notificationsOnPage->where('is_read', false)),
            ]
        ];
    }

    /**
     * Mark a specific notification as read for a user.
     */
    public function markNotificationAsRead(User $user, int $notificationId): bool
    {
        $notification = $user->notifications()->findOrFail($notificationId);
        $notification->update(['is_read' => true, 'read_at' => now()]);
        return true;
    }

    /**
     * Mark a specific notification as unread for a user.
     */
    public function markNotificationAsUnread(User $user, int $notificationId): bool
    {
        $notification = $user->notifications()->findOrFail($notificationId);
        $notification->update(['is_read' => false, 'read_at' => null]);
        return true;
    }

    /**
     * Mark all notifications as read for a user. 
     */
    public function markAllNotificationsAsRead(User $user): int
    {
        return $user->notifications()
            ->where('is_read', false)
            ->update(['is_read' => true, 'read_at' => now()]);
    }

    /**
     * Retrieve all notification settings for a user, including their current status. 
     */
    public function getNotificationSettings(User $user): array
    {
        $allTypes = NotificationType::with('translation')->where('is_visible', true)->get();
        $userSettings = UserNotificationSetting::where('user_id', $user->user_id)->get()->keyBy('notification_type_id');

        return $allTypes->map(function ($type) use ($userSettings) {
            $setting = $userSettings->get($type->notification_type_id);
            return [
                'type_id' => $type->notification_type_id,
                'name' => $type->name,
                'channels' => $type->default_channels,
                'event' => $type->event,
                'is_enabled' => $setting ? $setting->is_enabled : true,
            ];
        })->toArray();
    }

    /**
     * Update notification settings for a user. 
     */
    public function updateNotificationSettings(User $user, array $settings): void
    {
        foreach ($settings as $setting) {
            UserNotificationSetting::updateOrCreate(
                ['user_id' => $user->user_id, 'notification_type_id' => $setting['type_id']],
                ['is_enabled' => $setting['is_enabled']]
            );
        }
    }
}
