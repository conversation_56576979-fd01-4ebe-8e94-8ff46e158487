<?php

namespace App\Helpers;

use App\Enums\DefaultValues;
use App\Http\Resources\PlatformCountryResource;
use App\Models\BillingVatMapping;
use App\Models\Country;
use App\Models\Currency;
use App\Models\Plan;
use App\Models\PlanFeature;
use App\Models\PlatformCountry;
use App\Models\Subscription;
use App\Observers\SubscriptionObserver;
use App\Services\MoneyFormatter;
use Cknow\Money\Money;

class BillingsHelper
{

    /**
     * Address Types 
     */
    const ADDRESS_TYPE_COMPANY = 'company_address';

    const ADDRESS_TYPE_BILLING = 'billing_address';

    /**
     * Get Platform Billing Country VAT
     */
    /**
     * Get Platform Billing Country VAT based on the new rules.
     *
     * @param string|null $customerBillingCountry
     * @param string|null $platformBillingCountry
     * @return float
     */
    public static function getVat(?string $customerBillingCountry = null, ?string $platformBillingCountry = null): float
    {
        if (empty($customerBillingCountry) || empty($platformBillingCountry)) {
            return DefaultValues::VAT->get();
        }

        $specificVatCountries = config('settings.vat.specific_countries', ['SA', 'AE', 'QA']);
        $otherLocationPlaceholder = config('settings.vat.other_location_placeholder', 'AO');

        $customerLookupCountry = in_array($customerBillingCountry, $specificVatCountries)
            ? $customerBillingCountry
            : $otherLocationPlaceholder;

        $today = now()->toDateString();

        $vatMapping = BillingVatMapping::where('customer_billing_country_code', $customerLookupCountry)
            ->where('platform_billing_country_code', $platformBillingCountry)
            ->where(function ($query) use ($today) {
                $query->whereNull('from_date')->orWhere('from_date', '<=', $today);
            })
            ->where(function ($query) use ($today) {
                $query->whereNull('to_date')->orWhere('to_date', '>=', $today);
            })
            ->orderByDesc('billing_vat_mapping_id')
            ->first();

        if ($vatMapping) {
            return (float) $vatMapping->vat;
        }

        return DefaultValues::VAT->get();
    }

    /**
     * Currency Conversion From & To USD
     * From ( Plan Currency )
     * To ( Billing Currency )
     */
    public static function currencyConversion(float $price, string $fromISO, string $toISO): float
    {
        $fromISO = strtoupper($fromISO);
        $toISO = strtoupper($toISO);

        if ($fromISO === $toISO) {
            return $price;
        }

        $fromCurrency = Currency::where('iso', $fromISO)->first();
        $toCurrency = Currency::where('iso', $toISO)->first();

        if (!$fromCurrency || !$toCurrency || $fromCurrency->exchange_rate == 0 || $toCurrency->exchange_rate == 0) {
            return $price;
        }

        // Convert to USD then to destination currency
        $priceInUSD = $price / $fromCurrency->exchange_rate;
        $converted = $priceInUSD * $toCurrency->exchange_rate;

        return round($converted, 2); // precision-safe
    }


    /**
     * Get Currency by ISO code
     */
    public static function getCurrencyByISOCode(string $code): string
    {
        $currency = Currency::where('iso', strtoupper($code))->first();

        return $currency?->symbol ?? DefaultValues::CURRENCY->get();
    }

    /**
     * Calculates and formats the estimated price for a plan, addons, and applies VAT and currency conversions.
     */
    public static function calculateEstimatedPrice_old(
        $planId,
        $billingCountryId,
        $newCurrencyId = null,
        $addons = [],
        $platformCountryId = null,
        $numberOfYears = null,
        $subscriptionId = null
    ): array {

        $plan = Plan::find($planId);
        if (!$plan) {
            return [];
        }

        if ($subscriptionId && $newCurrencyId) {
            $subscriptionModel = Subscription::find($subscriptionId);
            if ($subscriptionModel) {
                SubscriptionObserver::$skipObserver = true;
                $subscriptionModel->update(['currency_id' => $newCurrencyId]);
                SubscriptionObserver::$skipObserver = false;
            }
        }

        $defaultPlanCurrency = $plan->currency();
        $priceInCents = PlanFeature::whereIn('plan_feature_id', $addons)->sum('price');

        if ($numberOfYears && $numberOfYears > 0) {
            $priceInCents *= $numberOfYears;
        }

        $price = new Money($priceInCents, $defaultPlanCurrency);
        $discount = new Money(0, $defaultPlanCurrency);
        $totalDiscounted = $price->subtract($discount);

        $billingCountry = Country::find($billingCountryId);
        $platformCountry = PlatformCountry::find($platformCountryId);

        $vatPercentage = self::getVat($billingCountry?->code, $platformCountry?->code);
        $vatAmount = $totalDiscounted->multiply($vatPercentage);
        $grandTotal = $totalDiscounted->add($vatAmount);

        $newCurrencyISO = $newCurrencyId ? Currency::find($newCurrencyId)?->iso : null;

        if ($newCurrencyISO && strtoupper($newCurrencyISO) !== strtoupper($defaultPlanCurrency)) {
            $convert = function (Money $money, string $targetCurrency) use ($defaultPlanCurrency) {
                $amountInBaseUnit = $money->divide(100)->getAmount();
                $convertedValue = self::currencyConversion($amountInBaseUnit, $defaultPlanCurrency, $targetCurrency);
                return new Money($convertedValue, $targetCurrency);
            };

            $price = $convert($price, $newCurrencyISO);
            $discount = $convert($discount, $newCurrencyISO);
            $totalDiscounted = $convert($totalDiscounted, $newCurrencyISO);
            $vatAmount = $convert($vatAmount, $newCurrencyISO);
            $grandTotal = $convert($grandTotal, $newCurrencyISO);
        }

        return [
            "price" => $price,
            "discount" => $discount,
            "total_discounted" => $totalDiscounted,
            "vat_percentage" => $vatPercentage,
            "vat_amount" => $vatAmount,
            "grand_total" => $grandTotal,
        ];
    }

    public static function calculateEstimatedPrice(
        $planId,
        $billingCountryId,
        $newCurrencyId = null,
        $addons = [],
        $platformCountryId = null,
        $numberOfYears = null,
        $subscriptionId = null
    ): array {

        $plan = Plan::find($planId);
        if (!$plan) {
            return [];
        }

        // This logic seems tangential to the price calculation itself.
        // If its goal is to update the subscription, it might be better placed elsewhere
        // to keep this function focused solely on calculation.
        if ($subscriptionId && $newCurrencyId) {
            $subscriptionModel = Subscription::find($subscriptionId);
            if ($subscriptionModel) {
                SubscriptionObserver::$skipObserver = true;
                $subscriptionModel->update(['currency_id' => $newCurrencyId]);
                SubscriptionObserver::$skipObserver = false;
            }
        }

        $defaultPlanCurrencyCode = $plan->currency();
        $defaultCurrency = new \Money\Currency($defaultPlanCurrencyCode);

        // 1. Calculate base price and discount in the ORIGINAL currency
        $priceInCents = PlanFeature::whereIn('plan_feature_id', $addons)->sum('price');
        $basePrice = new Money($priceInCents, $defaultCurrency);

        // Apply yearly multiplier if present
        if ($numberOfYears && $numberOfYears > 1) {
            $basePrice = $basePrice->multiply($numberOfYears);
        }

        // Assuming discount logic will be added here. For now, it's zero.
        $discount = new Money(0, $defaultCurrency);

        // 2. Determine target currency and CONVERT before further calculations
        $newCurrencyISO = $newCurrencyId ? Currency::find($newCurrencyId)?->iso : null;
        $isConversionNeeded = $newCurrencyISO && (strtoupper($newCurrencyISO) !== strtoupper($defaultPlanCurrencyCode));

        $finalPrice = $basePrice;
        $finalDiscount = $discount;
        $finalCurrencyCode = $defaultPlanCurrencyCode;

        if ($isConversionNeeded) {
            $finalCurrencyCode = $newCurrencyISO;

            // Helper function to perform a clean conversion
            $convert = function (Money $money, string $targetCurrencyCode) use ($defaultPlanCurrencyCode) {
                // Get amount as a float for precise conversion (e.g., 1050 cents -> 10.50)
                $amountAsFloat = (float) $money->getAmount() / 100;

                // Perform the conversion on the precise float value
                $convertedFloat = self::currencyConversion($amountAsFloat, $defaultPlanCurrencyCode, $targetCurrencyCode);

                // Convert back to cents/smallest unit for the new Money object
                $convertedInCents = (int) round($convertedFloat * 100);

                return new Money($convertedInCents, new \Money\Currency($targetCurrencyCode));
            };

            $finalPrice = $convert($basePrice, $newCurrencyISO);
            $finalDiscount = $convert($discount, $newCurrencyISO);
        }

        // 3. All calculations from here on use the FINAL currency
        $totalDiscounted = $finalPrice->subtract($finalDiscount);

        $billingCountry = Country::find($billingCountryId);
        $platformCountry = PlatformCountry::find($platformCountryId);

        // 4. Calculate VAT on the final, converted total
        $vatPercentage = self::getVat($billingCountry?->code, $platformCountry?->code);
        $vatAmount = $totalDiscounted->multiply($vatPercentage); // This will now correctly calculate VAT in the target currency

        $grandTotal = $totalDiscounted->add($vatAmount);

        return [
            "price" => $finalPrice, // renamed from 'price' to avoid confusion
            "discount" => $finalDiscount,
            "total_discounted" => $totalDiscounted,
            "vat_percentage" => $vatPercentage,
            "vat_amount" => $vatAmount,
            "grand_total" => $grandTotal,
        ];
    }

    /**
     * Get Deployment Countries Resources by Billing Country Code
     */
    public static function getDeploymentCountriesByBillingCountryCode(?string $billingCountryCode): array
    {
        $countryCodes = match ($billingCountryCode) {
            'SA' => ['SA', 'QA'],
            'AE' => ['AE', 'QA'],
            default => ['QA'],
        };

        $countries = PlatformCountry::whereIn('code', $countryCodes)->get();

        return PlatformCountryResource::collection($countries)->resolve();
    }
}
