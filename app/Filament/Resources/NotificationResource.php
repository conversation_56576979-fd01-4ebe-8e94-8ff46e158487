<?php

// app/Filament/Resources/NotificationResource.php
namespace App\Filament\Resources;

use App\Enums\Notifications;
use App\Filament\RelationManagers\CustomAuditsRelationManager;
use App\Filament\Resources\NotificationResource\Pages;
use App\Models\Notification;
use App\Models\NotificationType;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use App\Traits\DynamicFields;
use App\Traits\HasShieldAccess;

class NotificationResource extends Resource
{
    use DynamicFields, HasShieldAccess;

    protected static ?string $model = Notification::class;

    protected static ?string $navigationIcon = 'heroicon-o-bell';

    protected static ?string $navigationGroup = 'Notifications';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\Select::make('user_id')
                ->relationship('user', 'name')
                ->searchable()
                ->required()
                ->preload(),
            Forms\Components\Select::make('notification_type_id')
                ->relationship('notificationType', 'name')
                ->required()
                ->native(false)
                ->searchable()
                ->preload()
                ->live()
                ->afterStateUpdated(function (Set $set, ?string $state) {
                    if (is_null($state)) {
                        $set('template_params', []);
                        return;
                    }

                    $notificationType = NotificationType::find($state);

                    if (!$notificationType) {
                        return;
                    }

                    $translation = $notificationType->translation;

                    if (!$translation || !isset($translation->template_string)) {
                        $set('template_params', []);
                        return;
                    }

                    preg_match_all('/{(\w+)}/', $translation->template_string, $matches);

                    $placeholders = $matches[1] ?? [];

                    $params = array_fill_keys($placeholders, '');

                    $set('template_params', $params);
                }),
            Forms\Components\KeyValue::make('template_params')->editableKeys(false)->columnSpanFull(),
            Forms\Components\TextInput::make('link_url')->maxLength(2048)->columnSpanFull(),
            Forms\Components\Toggle::make('is_archived')->required(),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user.name')->label('User')->searchable()->sortable(),
                Tables\Columns\TagsColumn::make('delivery_channels')
                    ->label('Channel')
                    ->getStateUsing(fn($record) => $record->delivery_channels),
                Tables\Columns\TextColumn::make('notificationType.name')->label('Notification Type')->searchable()->sortable(),
                Tables\Columns\IconColumn::make('is_sent')->label('Sent')->boolean(),
                Tables\Columns\IconColumn::make('is_read')->label('Read')->boolean(),
                Tables\Columns\IconColumn::make('is_archived')->label('Archived')->boolean(),
                Tables\Columns\TextColumn::make('created_at')->dateTime()->sortable(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\ViewAction::make()
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            CustomAuditsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListNotifications::route('/'),
            'create' => Pages\CreateNotification::route('/create'),
            'view' => Pages\ViewNotification::route('/{record}'),
            'edit' => Pages\EditNotification::route('/{record}/edit'),
        ];
    }
}
