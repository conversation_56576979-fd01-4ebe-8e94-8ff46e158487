<?php

namespace App\Filament\Resources\SubscriptionResource\RelationManagers;

use App\Enums\Notifications;
use App\Enums\PaymentRejectionReason;
use App\Enums\SubscriptionLicenses;
use App\Enums\SubscriptionReceipts;
use App\Enums\Subscriptions;
use App\Helpers\AppHelper;
use App\Jobs\ProvisionClientResourcesJob;
use App\Mail\PaymentApprovedMail;
use App\Mail\PaymentRejectedMail;
use App\Models\Subscription;
use App\Observers\SubscriptionLicenseObserver;
use App\Observers\SubscriptionObserver;
use App\Services\MoneyFormatter;
use App\Services\NotificationService;
use App\Services\SubscriptionService;
use Cknow\Money\Money;
use Filament\Forms;
use Filament\Notifications\Notification;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class ReceiptsRelationManager extends RelationManager
{
    protected static string $relationship = 'subscriptionReceipts';

    protected static ?string $recordTitleAttribute = 'receipt_number';

    public static function canViewForRecord(\Illuminate\Database\Eloquent\Model $ownerRecord, string $pageClass): bool
    {
        return auth()->user()->can('view_subscription::receipt');
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ViewColumn::make('receipt_preview')
                    ->label('Receipt')
                    ->view('filament.components.receipt-preview'),
                Tables\Columns\TextColumn::make('receipt_number')
                    ->searchable(),
                Tables\Columns\TextColumn::make('amount')
                    ->formatStateUsing(function ($state, $record) {
                        $priceModel = new Money($state, $record->currency_code);
                        return app(MoneyFormatter::class)->price($priceModel->divide(100)->getAmount(), $priceModel->getCurrency())->format();
                    })
                    ->sortable(),
                // Tables\Columns\TextColumn::make('currency_code')
                //     ->searchable(),
                Tables\Columns\TextColumn::make('receipt_status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'approved' => 'success',
                        'rejected' => 'danger',
                        default => 'warning',
                    }),
                Tables\Columns\TextColumn::make('paid_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('approved_by')
                    ->toggleable()
                    ->formatStateUsing(function ($state) {
                        if (!$state) return '-';
                        $user = \App\Models\User::find($state);
                        return $user ? $user->name : $state;
                    }),

                Tables\Columns\TextColumn::make('rejected_by')
                    ->toggleable()
                    ->formatStateUsing(function ($state) {
                        if (!$state) return '-';
                        $user = \App\Models\User::find($state);
                        return $user ? $user->name : $state;
                    }),

                // todo:nc check if we need to show payment type? and what is the relation? (add relation in receipt)
                //                Tables\Columns\TextColumn::make('subscription.subscriptionLatestPayment.payment_type')
                //                    ->label('Payment Type')
                //                    ->formatStateUsing(function ($state, $record) {
                //                        if (empty($state)) {
                //                            return 'Not specified';
                //                        }
                //                        return ucfirst(str_replace('_', ' ', $state));
                //                    })
                //                    ->searchable()
                //                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\Action::make('uploadReceipt')
                    ->label('Upload Receipt')
                    ->icon('heroicon-o-paper-clip')
                    ->form([
                        Forms\Components\FileUpload::make('receipt_file')
                            ->label('Receipt File')
                            ->acceptedFileTypes(['application/pdf', 'image/jpeg', 'image/png', 'image/jpg'])
                            ->maxSize(5120)
                            ->disk('public') // Store temporarily in public disk
                            ->directory('temp-uploads')
                            ->required()
                    ])
                    ->action(function (array $data) {
                        $subscription = $this->getOwnerRecord();
                        // Create receipt record
                        $receiptModel = $subscription->subscriptionReceipts()->create([
                            'receipt_number' => uniqid(), // todo:nc check if there is specific format for receipt number
                            'amount' => $subscription->totalPrice(asArray: false),
                            'currency_code' => $subscription->plan->currency,
                            'receipt_status' => SubscriptionReceipts::RECEIPT_STATUS_PENDING->value,
                            'storage' => 'gcs',
                        ]);

                        // Handle file upload - Filament returns the path to the uploaded file
                        $filePath = $data['receipt_file'];

                        // Get the full path to the uploaded file
                        $fullPath = storage_path('app/public/' . $filePath);

                        // Generate receipt name
                        $receiptName = $subscription->subscriber->public_company_id . "_" . $subscription->public_subscription_id . "_" . $receiptModel->subscription_receipts_id;

                        // Get file contents
                        $fileContents = file_get_contents($fullPath);
                        $extension = pathinfo($fullPath, PATHINFO_EXTENSION);

                        // Create a temporary file with the contents
                        $tempFile = tempnam(sys_get_temp_dir(), 'receipt');
                        file_put_contents($tempFile, $fileContents);

                        // Create an UploadedFile instance
                        $file = new \Illuminate\Http\UploadedFile(
                            $tempFile,
                            basename($fullPath),
                            mime_content_type($tempFile),
                            null,
                            true
                        );

                        // Upload to GCS
                        $gcsService = app(\App\Services\GcsService::class)->setFolder('receipts')->setFileName($receiptName);
                        $receipt_path = $gcsService->upload($file);

                        // Clean up temporary files
                        @unlink($tempFile);
                        @unlink($fullPath);

                        // Update receipt with path
                        $receiptModel->update([
                            'receipt_path' => $receipt_path ?? '',
                        ]);

                        // Update subscription status if needed
                        if ($subscription->subscription_status === Subscriptions::SUBSCRIPTION_STATUS_PENDING_RECEIPT->value) {
                            /**
                             * Set Observer EventType
                             */
                            \App\Observers\SubscriptionObserver::$eventType = Subscriptions::HISTORY_EVENT_TYPE_PAYMENT_UPDATE->value;

                            $subscription->update([
                                'subscription_status' => Subscriptions::SUBSCRIPTION_STATUS_PENDING_PAYMENT->value,
                                'payment_status' => Subscriptions::SUBSCRIPTION_PAYMENT_STATUS_IN_REVIEW->value,
                            ]);
                        }

                        Notification::make()
                            ->title('Receipt Uploaded')
                            ->body('The receipt has been uploaded successfully and is pending review.')
                            ->success()
                            ->send();
                    })
                    ->visible(fn() => auth()->user()->can('create_subscription::receipt')),
            ])
            ->actions([
                Tables\Actions\Action::make('approve')
                    ->label('Approve')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->visible(fn($record) => $record->receipt_status === 'pending' && auth()->user()->can('update_subscription::receipt'))
                    ->requiresConfirmation()
                    ->modalHeading('Approve Receipt')
                    ->modalDescription('Are you sure you want to approve this receipt? This will update the subscription status.')
                    ->modalSubmitActionLabel('Yes, approve receipt')
                    ->action(function ($record) {
                        try {
                            // Update receipt status
                            $record->receipt_status = 'approved';
                            $record->approved_by = auth()->id();
                            $record->paid_at = now();
                            $record->save();

                            // Update subscription status
                            $subscription = $this->getOwnerRecord();

                            $nextStatus = $subscription->subscriptionHasFeaturePrefix(Subscription::SUBSCRIPTION_TYPE_ON_PREM)
                                ? Subscriptions::SUBSCRIPTION_STATUS_ACTIVE->value
                                : Subscriptions::SUBSCRIPTION_STATUS_PENDING_LICENSE_KEY->value;

                            SubscriptionObserver::$eventType = Subscriptions::HISTORY_EVENT_TYPE_PAYMENT_APPROVED->value;

                            $subscription->update([
                                'subscription_status' => $nextStatus,
                                'payment_status' => Subscriptions::SUBSCRIPTION_PAYMENT_STATUS_PAID->value,
                                'last_payment_date' => now(),
                            ]);

                            $subscriber = $subscription->subscriber;

                            // Send email notification
                            if ($subscriber && $subscriber?->primaryContact) {
                                Mail::to($subscriber?->primaryContact?->email)
                                    ->queue(new PaymentApprovedMail($subscription));
                            }

                            // Send subscription activated email if status is active
                            if ($nextStatus === Subscriptions::SUBSCRIPTION_STATUS_ACTIVE->value) {
                                SubscriptionService::sendSubscriptionActivatedEmail($subscription);
                            }


                            // Create notification for receipt approved
                            $notificationService = app(NotificationService::class);
                            if ($subscriber && $subscriber?->primaryContact) {
                                $notificationService->createNotification(
                                    $subscriber?->primaryContact,
                                    Notifications::NOTIFICATION_EVENT_RECEIPT_APPROVED->value,
                                    [
                                        'user_name' => $subscriber?->primaryContact?->name,
                                        'subscription_id' => $subscription->public_subscription_id
                                    ]
                                );
                            }

                            // If Saas: create license and dispatch provision job
                            if ($subscription->isSaas()) {
                                SubscriptionLicenseObserver::$skipObserver = true;

                                $clientNameIdentifier = "{$subscriber->name} {$subscription->id}";
                                $sonarUrl = Str::slug($clientNameIdentifier) . '.' . config('services.cloudflare.base_domain');
                                $normalizedUrl = "https://" . rtrim(AppHelper::normalizeDomain($sonarUrl), '/');

                                $domainAlreadyExists = $subscriber->companyLicenses()
                                    ->where('sonar_url', $normalizedUrl)
                                    ->exists();

                                $shouldNormalizeDomain = !((bool) $subscriber->hasPreviousEnvironment()) && !$domainAlreadyExists;

                                Log::info("domainAlreadyExists {$domainAlreadyExists} AND HasPevEnv {$shouldNormalizeDomain} AND Sonar URL: {$normalizedUrl}");

                                $sonarUrl = $shouldNormalizeDomain ? $normalizedUrl : "https://" . rtrim($sonarUrl, '/');

                                Log::info("Provisioning resources for client: {$clientNameIdentifier} with Sonar URL: {$sonarUrl}");

                                $subscription->subscriptionLicenses()->create([
                                    'company_id' => $subscriber->company_id,
                                    'license_type' => Subscriptions::LICENSE_TYPE_COMMERCIAL->value,
                                    'server_id' => null,
                                    'environment' => Subscriptions::ENVIRONMENT_OPTION_PRODUCTION->value,
                                    'sonar_url' => $sonarUrl,
                                    'sonar_username' => config('settings.sonarqube.default_username'),
                                    'sonar_password' => Crypt::encryptString(config('settings.sonarqube.default_password')),
                                    'sonar_api_token' => null,
                                    'environment_status' => SubscriptionLicenses::ENVIRONMENT_STATUS_PENDING->value,
                                ]);

                                ProvisionClientResourcesJob::dispatch($clientNameIdentifier, $shouldNormalizeDomain);

                                SubscriptionLicenseObserver::$skipObserver = false;
                            }

                            Notification::make()
                                ->title('Receipt Approved')
                                ->body('The receipt has been approved and the subscription status has been updated.')
                                ->success()
                                ->send();
                        } catch (\Throwable $e) {
                            Log::error('Receipt approval failed', [
                                'message' => $e->getMessage(),
                                'trace' => $e->getTraceAsString(),
                            ]);

                            Notification::make()
                                ->title('Approval Failed')
                                ->body('An unexpected error occurred while approving the receipt. Please check logs.')
                                ->danger()
                                ->send();
                        }
                    }),

                Tables\Actions\Action::make('reject')
                    ->label('Reject')
                    ->icon('heroicon-o-x-circle')
                    ->color('danger')
                    ->visible(fn($record) => $record->receipt_status === 'pending' && auth()->user()->can('update_subscription::receipt'))
                    ->form([
                        Forms\Components\Select::make('rejection_reason')
                            ->label('Rejection Reason')
                            ->options(PaymentRejectionReason::getValues())
                            ->required()
                            ->searchable(),
                        Forms\Components\Textarea::make('rejection_reason_description')
                            ->label('Additional Details')
                            ->placeholder('Provide additional details about the rejection reason')
                            ->maxLength(1000)
                    ])
                    ->action(function ($record, array $data) {
                        // Update receipt status
                        $record->receipt_status = 'rejected';
                        $record->rejected_by = auth()->id();
                        $record->rejection_reason = $data['rejection_reason'];
                        $record->save();

                        // Update subscription status
                        $subscription = $this->getOwnerRecord();

                        // Update subscription record
                        /**
                         * Set Observer EventType
                         */
                        SubscriptionObserver::$eventType = Subscriptions::HISTORY_EVENT_TYPE_PAYMENT_REJECTED->value;
                        SubscriptionObserver::$rejectionReasonDescription = $data['rejection_reason_description'] ?? null;
                        SubscriptionObserver::$rejectionReason = $data['rejection_reason'];

                        // update status - use fresh() to ensure the model is properly loaded
                        $subscription = $this->getOwnerRecord()->fresh();
                        $subscription->subscription_status = Subscriptions::SUBSCRIPTION_STATUS_PENDING_RECEIPT->value;
                        $subscription->payment_status = Subscriptions::SUBSCRIPTION_PAYMENT_STATUS_FAILED->value;
                        $subscription->save();

                        $subscriber = $subscription->subscriber;

                        // Send email notification
                        Mail::to($subscriber?->primarycontact?->email)
                            ->queue(new PaymentRejectedMail($subscription, $data['rejection_reason'], $data['rejection_reason_description'] ?? null));

                        // Create notification for receipt approved
                        $notificationService = app(NotificationService::class);
                        if ($subscriber && $subscriber?->primaryContact) {
                            $notificationService->createNotification(
                                $subscriber?->primaryContact,
                                Notifications::NOTIFICATION_EVENT_RECEIPT_REJECTED->value,
                                [
                                    'user_name' => $subscriber?->primaryContact?->name,
                                    'subscription_id' => $subscription->public_subscription_id
                                ]
                            );
                        }

                        Notification::make()
                            ->title('Receipt Rejected')
                            ->body('The receipt has been rejected and the subscription status has been updated.')
                            ->warning()
                            ->send();
                    }),
            ]);
    }
}
