<?php

/*
 * This file is part of the API Platform project.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace ApiPlatform\Core\Hydra\Serializer;

class_exists(\ApiPlatform\Hydra\Serializer\ErrorNormalizer::class);

if (false) {
    final class ErrorNormalizer extends \ApiPlatform\Hydra\Serializer\ErrorNormalizer
    {
    }
}
