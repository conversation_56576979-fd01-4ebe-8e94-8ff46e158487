<?php

/*
 * This file is part of the API Platform project.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace ApiPlatform\Core\Problem\Serializer;

class_exists(\ApiPlatform\Problem\Serializer\ErrorNormalizerTrait::class);

if (false) {
    trait ErrorNormalizerTrait
    {
        use \ApiPlatform\Problem\Serializer\ErrorNormalizerTrait;
    }
}
