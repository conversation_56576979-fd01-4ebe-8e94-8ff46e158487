<?php

/*
 * This file is part of the API Platform project.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace ApiPlatform\Core\GraphQl\Type\Definition;

class_exists(\ApiPlatform\GraphQl\Type\Definition\UploadType::class);

if (false) {
    final class UploadType extends \ApiPlatform\GraphQl\Type\Definition\UploadType
    {
    }
}
