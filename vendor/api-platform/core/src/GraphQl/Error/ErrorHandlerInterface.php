<?php

/*
 * This file is part of the API Platform project.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace ApiPlatform\GraphQl\Error;

use GraphQL\Error\Error;

/**
 * Handles the errors thrown by the GraphQL library.
 * It is responsible for applying the formatter to the errors and can be used for filtering or logging them.
 *
 * <AUTHOR> <<EMAIL>>
 */
interface ErrorHandlerInterface
{
    /**
     * @param Error[] $errors
     */
    public function __invoke(array $errors, callable $formatter): array;
}
