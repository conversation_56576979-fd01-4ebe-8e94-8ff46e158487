<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <services>

        <!-- Event listener -->

        <service id="api_platform.doctrine.listener.http_cache.purge" class="ApiPlatform\Doctrine\EventListener\PurgeHttpCacheListener">
            <argument type="service" id="api_platform.http_cache.purger" />
            <argument type="service" id="api_platform.iri_converter" />
            <argument type="service" id="api_platform.resource_class_resolver" />
            <argument type="service" id="api_platform.property_accessor" />
            <tag name="doctrine.event_listener" event="preUpdate" />
            <tag name="doctrine.event_listener" event="onFlush" />
            <tag name="doctrine.event_listener" event="postFlush" />
        </service>

    </services>

</container>
