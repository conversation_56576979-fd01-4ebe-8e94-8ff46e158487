Documentation for class Archive_Tar
===================================
Last update : 2001-08-15



Overview :
----------

  The Archive_Tar class helps in creating and managing GNU TAR format
  files compressed by GNU ZIP or not. 
  The class offers basic functions like creating an archive, adding
  files in the archive, extracting files from the archive and listing
  the archive content. 
  It also provide advanced functions that allow the adding and
  extraction of files with path manipulation. 


Sample :
--------

  // ----- Creating the object (uncompressed archive)
  $tar_object = new Archive_Tar("tarname.tar");
  $tar_object->setErrorHandling(PEAR_ERROR_PRINT);

  // ----- Creating the archive
  $v_list[0]="file.txt";
  $v_list[1]="data/";
  $v_list[2]="file.log";
  $tar_object->create($v_list);

  // ----- Adding files
  $v_list[0]="dev/file.txt";
  $v_list[1]="dev/data/";
  $v_list[2]="log/file.log";
  $tar_object->add($v_list);

  // ----- Adding more files
  $tar_object->add("release/newfile.log release/readme.txt");

  // ----- Listing the content
  if (($v_list  =  $tar_object->listContent()) != 0)
    for ($i=0; $i<sizeof($v_list); $i++)
    {
      echo "Filename :'".$v_list[$i][filename]."'<br>";
      echo " .size :'".$v_list[$i][size]."'<br>";
      echo " .mtime :'".$v_list[$i][mtime]."' (".date("l dS of F Y h:i:s A", $v_list[$i][mtime]).")<br>";
      echo " .mode :'".$v_list[$i][mode]."'<br>";
      echo " .uid :'".$v_list[$i][uid]."'<br>";
      echo " .gid :'".$v_list[$i][gid]."'<br>";
      echo " .typeflag :'".$v_list[$i][typeflag]."'<br>";
    }

  // ----- Extracting the archive in directory "install"
  $tar_object->extract("install");


Public arguments :
------------------

None


Public Methods :
----------------

Method : Archive_Tar($p_tarname, $compress = null)
Description :
  Archive_Tar Class constructor. This flavour of the constructor only
  declare a new Archive_Tar object, identifying it by the name of the
  tar file.
  If the compress argument is set the tar will be read or created as a
  gzip or bz2 compressed TAR file. 
Arguments :
  $p_tarname : A valid filename for the tar archive file.
  $p_compress : can be null, 'gz' or 'bz2'. For
                compatibility reason it can also be true. This
                parameter indicates if gzip or bz2 compression
                is required. 
Return value :
  The Archive_Tar object.
Sample :
  $tar_object = new Archive_Tar("tarname.tar");
  $tar_object_compressed = new Archive_Tar("tarname.tgz", true);
How it works :
  Initialize the object.

Method : create($p_filelist)
Description :
  This method creates the archive file and add the files / directories
  that are listed in $p_filelist. 
  If the file already exists and is writable, it is replaced by the
  new tar. It is a create and not an add. If the file exists and is
  read-only or is a directory it is not replaced. The method return
  false and a PEAR error text. 
  The $p_filelist parameter can be an array of string, each string
  representing a filename or a directory name with their path if
  needed. It can also be a single string with names separated by a
  single blank. 
  See also createModify() method for more details.
Arguments :
  $p_filelist : An array of filenames and directory names, or a single
  string with names separated by a single blank space. 
Return value :
  true on success, false on error.
Sample 1 :
  $tar_object = new Archive_Tar("tarname.tar");
  $tar_object->setErrorHandling(PEAR_ERROR_PRINT);  // Optional error handling
  $v_list[0]="file.txt";
  $v_list[1]="data/"; (Optional '/' at the end)
  $v_list[2]="file.log";
  $tar_object->create($v_list);
Sample 2 :
  $tar_object = new Archive_Tar("tarname.tar");
  $tar_object->setErrorHandling(PEAR_ERROR_PRINT);  // Optional error handling
  $tar_object->create("file.txt data/ file.log");
How it works :
  Just calling the createModify() method with the right parameters.

Method : createModify($p_filelist, $p_add_dir, $p_remove_dir = "")
Description :
  This method creates the archive file and add the files / directories
  that are listed in $p_filelist. 
  If the file already exists and is writable, it is replaced by the
  new tar. It is a create and not an add. If the file exists and is
  read-only or is a directory it is not replaced. The method return
  false and a PEAR error text. 
  The $p_filelist parameter can be an array of string, each string
  representing a filename or a directory name with their path if
  needed. It can also be a single string with names separated by a
  single blank. 
  The path indicated in $p_remove_dir will be removed from the
  memorized path of each file / directory listed when this path
  exists. By default nothing is removed (empty path "") 
  The path indicated in $p_add_dir will be added at the beginning of
  the memorized path of each file / directory listed. However it can
  be set to empty "". The adding of a path is done after the removing
  of path. 
  The path add/remove ability enables the user to prepare an archive
  for extraction in a different path than the origin files are. 
  See also addModify() method for file adding properties.
Arguments :
  $p_filelist : An array of filenames and directory names, or a single
                string with names separated by a single blank space.
  $p_add_dir : A string which contains a path to be added to the
               memorized path of each element in the list. 
  $p_remove_dir : A string which contains a path to be removed from
                  the memorized path of each element in the list, when
		  relevant.
Return value :
  true on success, false on error.
Sample 1 :
  $tar_object = new Archive_Tar("tarname.tar");
  $tar_object->setErrorHandling(PEAR_ERROR_PRINT);  // Optional error handling
  $v_list[0]="file.txt";
  $v_list[1]="data/"; (Optional '/' at the end)
  $v_list[2]="file.log";
  $tar_object->createModify($v_list, "install");
  // files are stored in the archive as :
  //   install/file.txt
  //   install/data
  //   install/data/file1.txt
  //   install/data/... all the files and sub-dirs of data/
  //   install/file.log
Sample 2 :
  $tar_object = new Archive_Tar("tarname.tar");
  $tar_object->setErrorHandling(PEAR_ERROR_PRINT);  // Optional error handling
  $v_list[0]="dev/file.txt";
  $v_list[1]="dev/data/"; (Optional '/' at the end)
  $v_list[2]="log/file.log";
  $tar_object->createModify($v_list, "install", "dev");
  // files are stored in the archive as :
  //   install/file.txt
  //   install/data
  //   install/data/file1.txt
  //   install/data/... all the files and sub-dirs of data/
  //   install/log/file.log
How it works :
  Open the file in write mode (erasing the existing one if one),
  call the _addList() method for adding the files in an empty archive,
  add the tar footer (512 bytes block), close the tar file.


Method : addModify($p_filelist, $p_add_dir, $p_remove_dir="")
Description :
  This method add the files / directories listed in $p_filelist at the
  end of the existing archive. If the archive does not yet exists it
  is created.
  The $p_filelist parameter can be an array of string, each string
  representing a filename or a directory name with their path if
  needed. It can also be a single string with names separated by a
  single blank. 
  The path indicated in $p_remove_dir will be removed from the
  memorized path of each file / directory listed when this path
  exists. By default nothing is removed (empty path "") 
  The path indicated in $p_add_dir will be added at the beginning of
  the memorized path of each file / directory listed. However it can
  be set to empty "". The adding of a path is done after the removing
  of path. 
  The path add/remove ability enables the user to prepare an archive
  for extraction in a different path than the origin files are. 
  If a file/dir is already in the archive it will only be added at the
  end of the archive. There is no update of the existing archived
  file/dir. However while extracting the archive, the last file will
  replace the first one. This results in a none optimization of the
  archive size. 
  If a file/dir does not exist the file/dir is ignored. However an
  error text is send to PEAR error. 
  If a file/dir is not readable the file/dir is ignored. However an
  error text is send to PEAR error. 
  If the resulting filename/dirname (after the add/remove option or
  not) string is greater than 99 char, the file/dir is
  ignored. However an error text is send to PEAR error. 
Arguments :
  $p_filelist : An array of filenames and directory names, or a single
                string with names separated by a single blank space. 
  $p_add_dir : A string which contains a path to be added to the
               memorized path of each element in the list. 
  $p_remove_dir : A string which contains a path to be removed from
                  the memorized path of each element in the list, when
		  relevant.
Return value :
  true on success, false on error.
Sample 1 :
  $tar_object = new Archive_Tar("tarname.tar");
  [...]
  $v_list[0]="dev/file.txt";
  $v_list[1]="dev/data/"; (Optional '/' at the end)
  $v_list[2]="log/file.log";
  $tar_object->addModify($v_list, "install");
  // files are stored in the archive as :
  //   install/file.txt
  //   install/data
  //   install/data/file1.txt
  //   install/data/... all the files and sub-dirs of data/
  //   install/file.log
Sample 2 :
  $tar_object = new Archive_Tar("tarname.tar");
  [...]
  $v_list[0]="dev/file.txt";
  $v_list[1]="dev/data/"; (Optional '/' at the end)
  $v_list[2]="log/file.log";
  $tar_object->addModify($v_list, "install", "dev");
  // files are stored in the archive as :
  //   install/file.txt
  //   install/data
  //   install/data/file1.txt
  //   install/data/... all the files and sub-dirs of data/
  //   install/log/file.log
How it works :
  If the archive does not exists it create it and add the files.
  If the archive does exists and is not compressed, it open it, jump
  before the last empty 512 bytes block (tar footer) and add the files
  at this point.
  If the archive does exists and is compressed, a temporary copy file
  is created. This temporary file is then 'gzip' read block by block
  until the last empty block. The new files are then added in the
  compressed file.
  The adding of files is done by going through the file/dir list,
  adding files per files, in a recursive way through the
  directory. Each time a path need to be added/removed it is done
  before writing the file header in the archive.

Method : add($p_filelist)
Description :
  This method add the files / directories listed in $p_filelist at the
  end of the existing archive. If the archive does not yet exists it
  is created. 
  The $p_filelist parameter can be an array of string, each string
  representing a filename or a directory name with their path if
  needed. It can also be a single string with names separated by a
  single blank. 
  See addModify() method for details and limitations.
Arguments :
  $p_filelist : An array of filenames and directory names, or a single
  string with names separated by a single blank space. 
Return value :
  true on success, false on error.
Sample 1 :
  $tar_object = new Archive_Tar("tarname.tar");
  [...]
  $v_list[0]="dev/file.txt";
  $v_list[1]="dev/data/"; (Optional '/' at the end)
  $v_list[2]="log/file.log";
  $tar_object->add($v_list);
Sample 2 :
  $tar_object = new Archive_Tar("tarname.tgz", true);
  [...]
  $v_list[0]="dev/file.txt";
  $v_list[1]="dev/data/"; (Optional '/' at the end)
  $v_list[2]="log/file.log";
  $tar_object->add($v_list);
How it works :
  Simply call the addModify() method with the right parameters.

Method : addString($p_filename, $p_string, $p_datetime, $p_params)
Description :
  This method add a single string as a file at the
  end of the existing archive. If the archive does not yet exists it
  is created.
Arguments :
  $p_filename : A string which contains the full filename path
                that will be associated with the string.
  $p_string :   The content of the file added in the archive.
  $p_datetime : (Optional) Timestamp of the file (default = now)
  $p_params :   (Optional) Various file metadata:
                    stamp - As above, timestamp of the file
                    mode - UNIX-style permissions (default 0600)
                    type - Is this a regular file or link (see TAR
                           format spec for how to create a hard/symlink)
                    uid - UNIX-style user ID (default 0 = root)
                    gid - UNIX-style group ID (default 0 = root)
Return value :
  true on success, false on error.
Sample 1 :
  $v_archive = & new Archive_Tar($p_filename);
  $v_archive->setErrorHandling(PEAR_ERROR_PRINT);
  $v_result = $v_archive->addString('data/test.txt', 'This is the text of the string');
  $v_result = $v_archive->addString(
                  'data/test.sh',
                  "#!/bin/sh\necho 'Hello'",
                  time(),
                  array( "mode" => 0755, "uid" => 34 )
              );


Method : extract($p_path = "")
Description :
  This method extract all the content of the archive in the directory
  indicated by $p_path.If $p_path is optional, if not set the archive
  is extracted in the current directory. 
  While extracting a file, if the directory path does not exists it is
  created. 
  See extractModify() for details and limitations.
Arguments :
  $p_path : Optional path where the files/dir need to by extracted.
Return value :
  true on success, false on error.
Sample :
  $tar_object = new Archive_Tar("tarname.tar");
  $tar_object->extract();
How it works :
  Simply call the extractModify() method with appropriate parameters.

Method : extractModify($p_path, $p_remove_path)
Description :
  This method extract all the content of the archive in the directory
  indicated by $p_path. When relevant the memorized path of the
  files/dir can be modified by removing the $p_remove_path path at the
  beginning of the file/dir path. 
  While extracting a file, if the directory path does not exists it is
  created. 
  While extracting a file, if the file already exists it is replaced
  without looking for last modification date. 
  While extracting a file, if the file already exists and is write
  protected, the extraction is aborted. 
  While extracting a file, if a directory with the same name already
  exists, the extraction is aborted. 
  While extracting a directory, if a file with the same name already
  exists, the extraction is aborted. 
  While extracting a file/directory if the destination directory exist
  and is write protected, or does not exist but can not be created,
  the extraction is aborted. 
  If after extraction an extracted file does not show the correct
  stored file size, the extraction is aborted. 
  When the extraction is aborted, a PEAR error text is set and false
  is returned. However the result can be a partial extraction that may
  need to be manually cleaned. 
Arguments :
  $p_path : The path of the directory where the files/dir need to by
            extracted. 
  $p_remove_path : Part of the memorized path that can be removed if
                   present at the beginning of the file/dir path. 
Return value :
  true on success, false on error.
Sample :
  // Imagine tarname.tar with files :
  //   dev/data/file.txt
  //   dev/data/log.txt
  //   readme.txt
  $tar_object = new Archive_Tar("tarname.tar");
  $tar_object->extractModify("install", "dev");
  // Files will be extracted there :
  //   install/data/file.txt
  //   install/data/log.txt
  //   install/readme.txt
How it works :
  Open the archive and call a more generic function that can extract
  only a part of the archive or all the archive. 
  See extractList() method for more details.

Method : extractInString($p_filename)
Description :
  This method extract from the archive one file identified by $p_filename.
  The return value is a string with the file content, or NULL on error. 
Arguments :
  $p_filename : The path of the file to extract in a string. 
Return value :
  a string with the file content or NULL.
Sample :
  // Imagine tarname.tar with files :
  //   dev/data/file.txt
  //   dev/data/log.txt
  //   dev/readme.txt
  $v_archive = & new Archive_Tar('tarname.tar');
  $v_archive->setErrorHandling(PEAR_ERROR_PRINT);
  $v_string = $v_archive->extractInString('dev/readme.txt');
  echo $v_string;

Method : listContent()
Description :
  This method returns an array of arrays that describe each
  file/directory present in the archive. 
  The array is not sorted, so it show the position of the file in the
  archive. 
  The file informations are :
    $file[filename] : Name and path of the file/dir.
    $file[mode] : File permissions (result of fileperms())
    $file[uid] : user id
    $file[gid] : group id
    $file[size] : filesize
    $file[mtime] : Last modification time (result of filemtime())
    $file[typeflag] : "" for file, "5" for directory
Arguments :
Return value :
  An array of arrays or 0 on error.
Sample :
  $tar_object = new Archive_Tar("tarname.tar");
  if (($v_list  =  $tar_object->listContent()) != 0)
    for ($i=0; $i<sizeof($v_list); $i++)
    {
      echo "Filename :'".$v_list[$i][filename]."'<br>";
      echo " .size :'".$v_list[$i][size]."'<br>";
      echo " .mtime :'".$v_list[$i][mtime]."' (".
           date("l dS of F Y h:i:s A", $v_list[$i][mtime]).")<br>";
      echo " .mode :'".$v_list[$i][mode]."'<br>";
      echo " .uid :'".$v_list[$i][uid]."'<br>";
      echo " .gid :'".$v_list[$i][gid]."'<br>";
      echo " .typeflag :'".$v_list[$i][typeflag]."'<br>";
    }
How it works :
  Call the same function as an extract however with a flag to only go
  through the archive without extracting the files. 

Method : extractList($p_filelist, $p_path = "", $p_remove_path = "")
Description :
  This method extract from the archive only the files indicated in the
  $p_filelist. These files are extracted in the current directory or
  in the directory indicated by the optional $p_path parameter. 
  If indicated the $p_remove_path can be used in the same way as it is
  used in extractModify() method. 
Arguments :
  $p_filelist : An array of filenames and directory names, or a single
                string with names separated by a single blank space. 
  $p_path : The path of the directory where the files/dir need to by
            extracted. 
  $p_remove_path : Part of the memorized path that can be removed if
                   present at the beginning of the file/dir path. 
Return value :
  true on success, false on error.
Sample :
  // Imagine tarname.tar with files :
  //   dev/data/file.txt
  //   dev/data/log.txt
  //   readme.txt
  $tar_object = new Archive_Tar("tarname.tar");
  $tar_object->extractList("dev/data/file.txt readme.txt", "install",
                           "dev");
  // Files will be extracted there :
  //   install/data/file.txt
  //   install/readme.txt
How it works :
  Go through the archive and extract only the files present in the
  list. 

