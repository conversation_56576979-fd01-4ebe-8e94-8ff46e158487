<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\Form\Extension\Core\DataTransformer;

use Symfony\Component\Form\Exception\TransformationFailedException;

/**
 * Transforms between an integer and a localized number with grouping
 * (each thousand) and comma separators.
 *
 * <AUTHOR> <b<PERSON><PERSON><PERSON>@gmail.com>
 */
class IntegerToLocalizedStringTransformer extends NumberToLocalizedStringTransformer
{
    /**
     * Constructs a transformer.
     *
     * @param bool        $grouping     Whether thousands should be grouped
     * @param int         $roundingMode One of the ROUND_ constants in this class
     * @param string|null $locale       locale used for transforming
     */
    public function __construct($grouping = false, $roundingMode = self::ROUND_DOWN, $locale = null)
    {
        if (\is_int($grouping) || \is_bool($roundingMode) || \is_int($locale)) {
            @trigger_error(sprintf('Passing a precision as the first value to %s::__construct() is deprecated since Symfony 4.2 and support for it will be dropped in 5.0.', __CLASS__), \E_USER_DEPRECATED);

            $grouping = $roundingMode;
            $roundingMode = $locale ?? self::ROUND_DOWN;
            $locale = null;
        }

        parent::__construct(0, $grouping, $roundingMode, $locale);
    }

    /**
     * {@inheritdoc}
     */
    public function reverseTransform($value)
    {
        $decimalSeparator = $this->getNumberFormatter()->getSymbol(\NumberFormatter::DECIMAL_SEPARATOR_SYMBOL);

        if (\is_string($value) && str_contains($value, $decimalSeparator)) {
            throw new TransformationFailedException(sprintf('The value "%s" is not a valid integer.', $value));
        }

        $result = parent::reverseTransform($value);

        return null !== $result ? (int) $result : null;
    }

    /**
     * @internal
     */
    protected function castParsedValue($value)
    {
        return $value;
    }
}
