<?php
/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License version 3.0
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/AFL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/AFL-3.0 Academic Free License version 3.0
 */

declare(strict_types=1);

namespace PrestaShop\Module\LoyaltyRewardPoints\Grid\Definition\Factory;

if (!defined('_PS_VERSION_')) {
    exit;
}

use PrestaShop\PrestaShop\Core\Grid\Action\Row\RowActionCollection;
use PrestaShop\PrestaShop\Core\Grid\Action\Row\Type\LinkRowAction;
use PrestaShop\PrestaShop\Core\Grid\Action\Row\Type\SubmitRowAction;
use PrestaShop\PrestaShop\Core\Grid\Column\ColumnCollection;
use PrestaShop\PrestaShop\Core\Grid\Column\Type\Common\ActionColumn;
//use PrestaShop\PrestaShop\Core\Grid\Column\Type\Common\DataColumn;
use PrestaShop\PrestaShop\Core\Grid\Column\Type\DataColumn;
use PrestaShop\PrestaShop\Core\Grid\Definition\Factory\AbstractGridDefinitionFactory;
use PrestaShop\PrestaShop\Core\Grid\Filter\FilterCollection;

class RuleGridDefinitionFactory extends AbstractGridDefinitionFactory
{
    const GRID_ID = 'lrp_rule';

    /**
     * {@inheritdoc}
     */
    protected function getId()
    {
        return self::GRID_ID;
    }

    /**
     * {@inheritdoc}
     */
    protected function getName()
    {
        return $this->trans('Loyalty Reward Points', [], 'Modules.Loyaltyrewardpoints.Admin');
    }

    /**
     * {@inheritdoc}
     */
    protected function getColumns()
    {
        return (new ColumnCollection())
            ->add(
                (new DataColumn('id_lrp_config_rule'))
                    ->setName($this->trans('Id', [], 'Modules.Loyaltyrewardpoints.Admin'))
                    ->setOptions([
                        'field' => 'id_lrp_config_rule',
                    ])
            )
            ->add(
                (new DataColumn('name'))
                    ->setName($this->trans('Name', [], 'Modules.Loyaltyrewardpoints.Admin'))
                    ->setOptions([
                        'field' => 'name',
                    ])
            )
            ->add(
                (new DataColumn('operator'))
                    ->setName($this->trans('Impact Type', [], 'Modules.Loyaltyrewardpoints.Admin'))
                    ->setOptions([
                        'field' => 'operator',
                    ])
            )
            ->add(
                (new DataColumn('point_impact'))
                    ->setName($this->trans('Points Impact', [], 'Modules.Loyaltyrewardpoints.Admin'))
                    ->setOptions([
                        'field' => 'point_impact',
                    ])
            )
            ->add(
                (new DataColumn('bln_enabled'))
                    ->setName($this->trans('Enabled?', [], 'Modules.Loyaltyrewardpoints.Admin'))
                    ->setOptions([
                        'field' => 'bln_enabled',
                    ])
            )
            ->add(
                (new ActionColumn('actions'))
                    ->setOptions([
                        'actions' => (new RowActionCollection())
                            ->add(
                                (new LinkRowAction('edit'))
                                    ->setName($this->trans('Edit', [], 'Admin.Actions'))
                                    ->setIcon('edit')
                                    ->setOptions([
                                        'route' => 'loyaltyrewardpoints_admin_config_rule_edit',
                                        'route_param_name' => 'id_lrp_config_rule',
                                        'route_param_field' => 'id_lrp_config_rule',
                                    ])
                            )
                            ->add(
                                (new SubmitRowAction('delete'))
                                    ->setName($this->trans('Delete', [], 'Admin.Actions'))
                                    ->setIcon('delete')
                                    ->setOptions([
                                        'method' => 'POST',
                                        'route' => 'loyaltyrewardpoints_admin_config_rule_delete',
                                        'route_param_name' => 'id_lrp_config_rule',
                                        'route_param_field' => 'id_lrp_config_rule',
                                        'confirm_message' => $this->trans(
                                            'Delete selected item?',
                                            [],
                                            'Admin.Notifications.Warning'
                                        ),
                                    ])
                            ),
                    ])
            );
    }

    protected function getFilters()
    {
        return new FilterCollection();
    }
}
