services:

  # Option form
  prestashop.module.loyaltyrewardpoints.form.config.tier_icon:
    class: 'PrestaShop\Module\LoyaltyRewardPoints\Form\Config\TierIconFormType'
    parent: 'form.type.translatable.aware'
    public: true
    arguments:
      - '@request_stack'
      - '@=service("prestashop.module.loyaltyrewardpoints.service.configuration")'

    tags:
      - { name: form.type }

  prestashop.module.loyaltyrewardpoints.form.config.tier_icon.data_provider:
    class: 'PrestaShop\Module\LoyaltyRewardPoints\Form\Config\TierIconFormDataProvider'
    public: true
    arguments:
      - "@=service('prestashop.adapter.legacy.context').getContext()"
      - '@=service("prestashop.module.loyaltyrewardpoints.service.admin.configuration")'

  prestashop.module.loyaltyrewardpoints.form.config.tier_icon.data_handler:
    class: 'PrestaShop\PrestaShop\Core\Form\Handler'
    public: true
    arguments:
      - '@form.factory'
      - '@prestashop.core.hook.dispatcher'
      - '@prestashop.module.loyaltyrewardpoints.form.config.tier_icon.data_provider'
      - 'PrestaShop\Module\LoyaltyRewardPoints\Form\Config\TierIconFormType'
      - 'AdminConfigTierIconForm'
