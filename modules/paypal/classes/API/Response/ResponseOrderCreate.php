<?php
/*
 * Since 2007 PayPal
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 *  versions in the future. If you wish to customize PrestaShop for your
 *  needs please refer to http://www.prestashop.com for more information.
 *
 *  <AUTHOR> 2007 PayPal
 *  <AUTHOR> ecommerce <<EMAIL>>
 *  @license http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 *  @copyright PayPal
 *
 */

namespace PaypalAddons\classes\API\Response;

if (!defined('_PS_VERSION_')) {
    exit;
}

class ResponseOrderCreate extends Response
{
    /** @var string */
    protected $approveLink;

    /** @var string */
    protected $paymentId;

    /** @var int */
    protected $statusCode;

    /**
     * @return string
     */
    public function getApproveLink()
    {
        return (string) $this->approveLink;
    }

    /**
     * @param string $approveLink
     */
    public function setApproveLink($approveLink)
    {
        $this->approveLink = $approveLink;

        return $this;
    }

    /**
     * @return string
     */
    public function getPaymentId()
    {
        return (string) $this->paymentId;
    }

    /**
     * @param string $paymentId
     */
    public function setPaymentId($paymentId)
    {
        $this->paymentId = $paymentId;

        return $this;
    }

    /**
     * @return int
     */
    public function getStatusCode()
    {
        return (int) $this->statusCode;
    }

    /**
     * @param int $statusCode
     */
    public function setStatusCode($statusCode)
    {
        $this->statusCode = $statusCode;

        return $this;
    }
}
